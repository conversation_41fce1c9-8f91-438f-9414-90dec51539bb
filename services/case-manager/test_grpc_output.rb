require 'logger'

# Create a logger to capture output
logger = Logger.new('/tmp/grpc_test.log')
logger.info "Starting RemoteUser GRPC test..."

begin
  logger.info "Creating RemoteUser with ID 193..."
  user = RemoteUser.new(id: 193)
  logger.info "User created successfully"
  
  logger.info "Fetching user name..."
  name = user.name
  logger.info "User name: #{name}"
  
  logger.info "Fetching user email..."
  email = user.email
  logger.info "User email: #{email}"
  
rescue => e
  logger.error "Error occurred: #{e.message}"
  logger.error "Error class: #{e.class}"
  logger.error "Backtrace: #{e.backtrace.first(5).join("\n")}"
end

logger.info "Test completed"

# Also write to stdout
puts "Test completed - check /tmp/grpc_test.log for results"
