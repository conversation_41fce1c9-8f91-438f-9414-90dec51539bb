# Case Management API Documentation

## Overview

This document outlines the API structure for the Athar Case Management System based on the progressive form loading architecture. The API follows JSON:API specification and includes authentication, authorization, progressive form completion workflows, and dynamic sub-form management for modal dialogs.

**Key Features:**

- **Progressive Form Loading**: Load only what's needed when needed
- **Dynamic Sub-Forms**: Modal dialogs managed through hierarchical FormSubmissions
- **Integrated Case Creation**: Cases created within the first form submission
- **Context-Aware Fields**: Server-side calculation of field visibility and enabled state
- **Real-time Progress Tracking**: Live updates of form completion status

## Base Configuration

- **Base URL**: `/api/v1`
- **Format**: JSON:API compliant
- **Authentication**: Bearer token (AtharAuth integration)
- **Authorization**: Role-based with project scoping
- **Pagination**: JSON:API standard with metadata

## Authentication

All API requests require authentication via Bearer token:

```http
Authorization: Bearer <session_token>
```

### Authentication Flow

```http
POST /api/session/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password",
  "project_id": "humanitarian_project_123"
}
```

**Response:**

```json
{
  "data": {
    "id": "user_123",
    "type": "user",
    "attributes": {
      "email": "<EMAIL>",
      "name": "Ahmed Al-Rashid",
      "roles": ["case_manager"],
      "project_id": "humanitarian_project_123"
    }
  },
  "meta": {
    "session_token": "eyJhbGciOiJIUzI1NiJ9...",
    "expires_at": "2025-01-21T10:00:00Z"
  }
}
```

## Core API Endpoints

### 1. Progressive Case Creation & Management

#### New Case Creation Flow (Integrated with First Form)

**Step 1: Start New Case with First Form Template**

```http
GET /api/v1/form_templates?filter[template_type]=workflow&sort=sequence_order
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "consent_assent",
      "type": "form_template",
      "attributes": {
        "name": "consent_assent",
        "title": "الموافقة والإقرار",
        "template_type": "workflow",
        "sequence_order": 1
      }
    }
  ]
}
```

**Step 2: Load First Template Sections**

```http
GET /api/v1/form_sections?template_id=consent_assent
Authorization: Bearer <token>
```

**Step 3: Create Case + Form Submission Together**

```http
POST /api/v1/form_submissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "form_submission",
    "attributes": {
      "form_template_id": "consent_assent",
      "form_section_id": "beneficiary_details",
      "assigned_user_id": "user_789",
      "case_type": "child_protection",
      "priority_level": 2,
      "form_data": {
        "beneficiary_name": "محمد الأحمد",
        "beneficiary_gender": "male",
        "beneficiary_age": 16,
        "beneficiary_nationality": "Syrian",
        "consent_given": true,
        "consent_date": "2025-01-20"
      }
    }
  }
}
```

**Response:**

```json
{
  "data": {
    "id": "fs_001",
    "type": "form_submission",
    "attributes": {
      "case_id": "case_123",
      "form_template_id": "consent_assent",
      "status": "draft",
      "created_at": "2025-01-20T10:00:00Z"
    },
    "relationships": {
      "case": {
        "data": { "id": "case_123", "type": "case" }
      }
    }
  },
  "included": [
    {
      "id": "case_123",
      "type": "case",
      "attributes": {
        "case_number": "CASE-2025-001",
        "beneficiary_name": "محمد الأحمد",
        "beneficiary_gender": "male",
        "status": "draft"
      }
    }
  ]
}
```

#### List Existing Cases

```http
GET /api/v1/cases?page[number]=1&page[size]=25&filter[status]=in_progress&include=assigned_user
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "case_123",
      "type": "case",
      "attributes": {
        "case_number": "CASE-2025-001",
        "status": "active",
        "priority_level": "high",
        "beneficiary_name": "محمد الأحمد",
        "beneficiary_gender": "male",
        "beneficiary_age": 16,
        "created_at": "2025-01-20T10:00:00Z",
        "updated_at": "2025-01-20T15:30:00Z",
        "overall_completion_percentage": 65,
        "approval_status": "pending"
      },
      "relationships": {
        "assigned_user": {
          "data": { "id": "user_789", "type": "user" }
        }
      }
    }
  ],
  "included": [
    {
      "id": "user_789",
      "type": "user",
      "attributes": {
        "name": "Sarah Johnson",
        "email": "<EMAIL>"
      }
    }
  ],
  "meta": {
    "total_count": 250,
    "current_page": 1,
    "per_page": 25,
    "total_pages": 10
  }
}
```

#### Get Case Progress (Progressive Loading)

```http
GET /api/v1/cases/123/progress
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "case_id": "case_123",
    "case_number": "CASE-2025-001",
    "status": "active",
    "overall_completion_percentage": 65,
    "can_submit_for_approval": false,
    "next_required_form": "comprehensive_assessment",
    "form_completion_summary": {
      "workflow_templates": [
        {
          "template_id": "consent_assent",
          "template_name": "الموافقة والإقرار",
          "sequence_order": 1,
          "completion_percentage": 100,
          "is_complete": true,
          "is_accessible": true,
          "completed_at": "2025-01-20T10:30:00Z"
        },
        {
          "template_id": "registration_rapid_assessment",
          "template_name": "التسجيل والتقييم السريع",
          "sequence_order": 2,
          "completion_percentage": 80,
          "is_complete": false,
          "is_accessible": true,
          "sections_completed": 4,
          "sections_total": 5
        },
        {
          "template_id": "comprehensive_assessment",
          "template_name": "التقييم الشامل",
          "sequence_order": 3,
          "completion_percentage": 0,
          "is_complete": false,
          "is_accessible": true,
          "prerequisites_met": true
        },
        {
          "template_id": "case_closure",
          "template_name": "إغلاق الحالة",
          "sequence_order": 4,
          "completion_percentage": 0,
          "is_complete": false,
          "is_accessible": false,
          "prerequisites_met": false,
          "blocked_by": ["comprehensive_assessment"]
        }
      ]
    }
  }
}
```

#### Submit Case for Approval

```http
POST /api/v1/cases/123/submit_for_approval
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "approval_submission",
    "attributes": {
      "notes": "All required forms completed. Case ready for supervisor review."
    }
  }
}
```

### 2. Progressive Form Management

#### List Form Templates by Type

```http
GET /api/v1/form_templates?filter[template_type]=workflow&sort=sequence_order
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "consent_assent",
      "type": "form_template",
      "attributes": {
        "name": "consent_assent",
        "title": "الموافقة والإقرار",
        "template_type": "workflow",
        "sequence_order": 1,
        "sections_count": 2,
        "estimated_completion_time": 15
      }
    },
    {
      "id": "registration_rapid_assessment",
      "type": "form_template",
      "attributes": {
        "name": "registration_rapid_assessment",
        "title": "التسجيل والتقييم السريع",
        "template_type": "workflow",
        "sequence_order": 2,
        "sections_count": 6,
        "estimated_completion_time": 45
      }
    }
  ]
}
```

#### Load Form Sections for Template

```http
GET /api/v1/form_sections?template_id=registration_rapid_assessment
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "case_information",
      "type": "form_section",
      "attributes": {
        "name": "case_information",
        "title": "معلومات الحالة",
        "section_type": "form",
        "display_order": 1,
        "is_required": true,
        "fields_count": 5
      }
    },
    {
      "id": "service_instances",
      "type": "form_section",
      "attributes": {
        "name": "service_instances",
        "title": "الخدمات",
        "section_type": "list",
        "display_order": 2,
        "sub_form_template_name": "service_instance",
        "sub_form_button_label": "إضافة خدمة +",
        "max_sub_forms": 20
      }
    }
  ]
}
```

#### Load Form Fields for Section (Context-Aware)

**For Form-Type Sections (Inline Fields):**

```http
GET /api/v1/form_sections/case_information/form_fields?case_id=123
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "beneficiary_name",
      "type": "form_field",
      "attributes": {
        "field_name": "beneficiary_name",
        "label": "اسم المستفيد",
        "field_type": "text",
        "required": true,
        "display_order": 1,
        "current_value": "محمد الأحمد",
        "is_enabled": true,
        "is_visible": true,
        "validation_rules": {
          "min_length": 2,
          "max_length": 100
        }
      }
    },
    {
      "id": "beneficiary_age",
      "type": "form_field",
      "attributes": {
        "field_name": "beneficiary_age",
        "label": "العمر",
        "field_type": "calculated_field",
        "calculation_formula": "age_from_date",
        "depends_on": ["beneficiary_date_of_birth"],
        "current_value": 16,
        "is_enabled": false,
        "is_visible": true
      }
    }
  ]
}
```

**For List-Type Sections (Item Collections):**

```http
GET /api/v1/form_sections/service_instances/form_fields?case_id=123
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "section_type": "list",
    "sub_form_template_name": "service_instance",
    "sub_form_button_label": "إضافة خدمة +",
    "max_sub_forms": 20,
    "current_count": 2,
    "can_add_more": true,
    "sub_submissions": [
      {
        "id": "sub_001",
        "form_data": {
          "service_name": "الاستشارة النفسية",
          "provider": "مركز الدعم النفسي",
          "due_date": "2025-02-15"
        },
        "submission_order": 1,
        "created_at": "2025-01-20T10:00:00Z"
      },
      {
        "id": "sub_002",
        "form_data": {
          "service_name": "التعليم البديل",
          "provider": "مركز التعليم المجتمعي",
          "due_date": "2025-03-01"
        },
        "submission_order": 2,
        "created_at": "2025-01-20T11:00:00Z"
      }
    ]
  }
}
```

#### Update Form Submission Data

```http
PATCH /api/v1/form_submissions/fs_001
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "form_submission",
    "attributes": {
      "form_data": {
        "beneficiary_name": "محمد أحمد الخليل",
        "beneficiary_phone": "+963-123-456789",
        "consent_given": true
      }
    }
  }
}
```

### 3. Dynamic Sub-Form Management (Modal Dialogs)

#### Create Component Submission (Service, Follow-up, etc.)

```http
POST /api/v1/form_submissions/component_submissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "component_submission",
    "attributes": {
      "case_id": "case_123",
      "form_section_id": "service_instances",
      "form_data": {
        "service_name": "خدمة الدعم النفسي",
        "intervention_reason": "تحسين الحالة النفسية للطفل",
        "provider": "مركز الدعم النفسي",
        "due_date": "2025-02-15",
        "monitoring_schedule": "weekly"
      }
    }
  }
}
```

**Response:**

```json
{
  "data": {
    "id": "sub_003",
    "type": "form_submission",
    "attributes": {
      "case_id": "case_123",
      "form_section_id": "service_instances",
      "submission_order": 3,
      "form_data": {
        "service_name": "خدمة الدعم النفسي",
        "intervention_reason": "تحسين الحالة النفسية للطفل",
        "provider": "مركز الدعم النفسي",
        "due_date": "2025-02-15",
        "monitoring_schedule": "weekly"
      },
      "created_at": "2025-01-20T12:00:00Z"
    }
  }
}
```

#### Get Section Submissions (List Items)

```http
GET /api/v1/form_submissions/fs_001/section_submissions?section_id=service_instances
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "sub_001",
      "type": "form_submission",
      "attributes": {
        "submission_order": 1,
        "form_data": {
          "service_name": "الاستشارة النفسية",
          "provider": "مركز الدعم النفسي"
        },
        "created_at": "2025-01-20T10:00:00Z"
      }
    },
    {
      "id": "sub_002",
      "type": "form_submission",
      "attributes": {
        "submission_order": 2,
        "form_data": {
          "service_name": "التعليم البديل",
          "provider": "مركز التعليم المجتمعي"
        },
        "created_at": "2025-01-20T11:00:00Z"
      }
    }
  ]
}
```

#### Update Component Submission

```http
PATCH /api/v1/form_submissions/sub_001
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "form_submission",
    "attributes": {
      "form_data": {
        "service_name": "الاستشارة النفسية المتقدمة",
        "due_date": "2025-02-20",
        "notes": "تم تحديث موعد الخدمة"
      }
    }
  }
}
```

#### Delete Component Submission

```http
DELETE /api/v1/form_submissions/sub_001
Authorization: Bearer <token>
```

### 4. Field-Specific Operations

#### Get Field Lookup Options

```http
GET /api/v1/form_fields/service_type/lookup_options?search=نفسي&locale=ar
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "field_id": "service_type",
    "lookup_source_type": "service_categories",
    "options": [
      {
        "id": "psychological_support",
        "name": "الدعم النفسي",
        "category": "protection"
      },
      {
        "id": "psychological_counseling",
        "name": "الاستشارة النفسية",
        "category": "protection"
      }
    ],
    "meta": {
      "total_options": 2,
      "search_term": "نفسي",
      "filters_applied": []
    }
  }
}
```

#### Calculate Field Value

```http
POST /api/v1/form_fields/beneficiary_age/calculate
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "field_calculation",
    "attributes": {
      "depends_on": {
        "beneficiary_date_of_birth": "2008-03-15"
      }
    }
  }
}
```

**Response:**

```json
{
  "data": {
    "id": "beneficiary_age",
    "type": "calculated_field",
    "attributes": {
      "calculated_value": 16,
      "calculation_performed_at": "2025-01-20T10:00:00Z"
    }
  }
}
```

### 5. Comments System

#### Case-Level Comments

##### List Case Comments

```http
GET /api/v1/cases/123/comments?include=user,replies
Authorization: Bearer <token>
```

##### Create Case Comment

```http
POST /api/v1/cases/123/comments
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "comment",
    "attributes": {
      "content": "Case requires additional documentation for family tracing.",
      "comment_type": "case_comment",
      "priority": "high"
    }
  }
}
```

#### Field-Level Comments

##### Create Field Comment

```http
POST /api/v1/form_fields/date_of_birth/comments
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "comment",
    "attributes": {
      "content": "Date of birth needs verification - documents show different date.",
      "comment_type": "field_comment",
      "case_id": "case_123"
    }
  }
}
```

##### Resolve Comment

```http
PATCH /api/v1/cases/123/comments/456/resolve
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "comment",
    "attributes": {
      "resolution_notes": "Documentation verified and updated."
    }
  }
}
```

### 6. Approval Workflow

#### List Pending Approvals (Supervisor)

```http
GET /api/v1/case_approvals?filter[status]=pending&include=case,case_manager
Authorization: Bearer <token>
```

#### Approve/Reject Case

```http
PATCH /api/v1/case_approvals/123
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "case_approval",
    "attributes": {
      "status": "approved",
      "notes": "All documentation complete. Case approved for service delivery.",
      "approved_at": "2025-01-20T16:00:00Z"
    }
  }
}
```

## Error Responses

### Standard Error Format

```json
{
  "errors": [
    {
      "id": "validation_error",
      "status": "422",
      "code": "VALIDATION_FAILED",
      "title": "Validation Failed",
      "detail": "First name is required",
      "source": {
        "pointer": "/data/attributes/first_name"
      }
    }
  ]
}
```

### Common Error Codes

- `401` - Unauthorized (invalid/expired token)
- `403` - Forbidden (insufficient permissions)
- `404` - Resource not found
- `422` - Validation errors
- `429` - Rate limit exceeded
- `500` - Internal server error

## Filtering and Sorting

### Available Filters

- `filter[status]` - Case status (open, in_progress, closed, suspended)
- `filter[priority_level]` - Priority (low, medium, high, urgent)
- `filter[assigned_user_id]` - Assigned user
- `filter[created_at][gte]` - Created after date
- `filter[approval_status]` - Approval status

### Sorting Options

- `sort=created_at` - Sort by creation date
- `sort=-updated_at` - Sort by last update (descending)
- `sort=priority_level,created_at` - Multiple sort fields

## Rate Limiting

- **Standard endpoints**: 100 requests per minute
- **Form submissions**: 20 requests per minute
- **File uploads**: 10 requests per minute

## Progressive Form Completion Workflow

### Complete Workflow Example

**1. Start New Case**

```http
GET /api/v1/form_templates?filter[template_type]=workflow&sort=sequence_order
```

**2. Load First Template Sections**

```http
GET /api/v1/form_sections?template_id=consent_assent
```

**3. Load Section Fields**

```http
GET /api/v1/form_sections/beneficiary_details/form_fields
```

**4. Create Case + First Form Submission**

```http
POST /api/v1/form_submissions
{
  "form_template_id": "consent_assent",
  "assigned_user_id": "user_789",
  "case_type": "child_protection",
  "form_data": {
    "beneficiary_name": "محمد الأحمد",
    "beneficiary_gender": "male",
    "consent_given": true
  }
}
```

**5. Check Case Progress**

```http
GET /api/v1/cases/123/progress
```

**6. Load Next Template**

```http
GET /api/v1/form_sections?template_id=registration_rapid_assessment
```

**7. Continue Progressive Form Flow...**

### Template Types

**Workflow Templates** (`template_type: 'workflow'`):

- Main case workflow steps (consent, registration, assessment, closure)
- Sequential order with prerequisites
- Rendered as main form pages

**Component Templates** (`template_type: 'component'`):

- Modal dialogs and standalone forms
- Used for services, follow-ups, family members
- Rendered as modal dialogs within workflow templates

### Section Types

**Form Sections** (`section_type: 'form'`):

- Fields rendered directly in the page
- Traditional form sections with inline fields
- Auto-save on field changes

**List Sections** (`section_type: 'list'`):

- Collections of items managed via modal dialogs
- Each item is a component submission
- Add/edit/delete operations through modals

## Dashboard and Metrics

### Supervisor Dashboard

```http
GET /api/v1/dashboard?include=case_metrics,team_metrics,comment_metrics
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "id": "supervisor_dashboard",
    "type": "dashboard",
    "attributes": {
      "case_metrics": {
        "total_cases": 150,
        "pending_approval": 12,
        "in_progress": 89,
        "completed_this_month": 23,
        "overdue_cases": 5
      },
      "team_metrics": {
        "total_case_managers": 8,
        "average_caseload": 18.75,
        "top_performers": [
          {
            "assigned_user_id": "user_123",
            "name": "Ahmed Al-Rashid",
            "completed_cases": 8,
            "avg_completion_time": 12.5
          }
        ]
      },
      "comment_metrics": {
        "total_unresolved_comments": 34,
        "case_level_comments": 18,
        "field_level_comments": 16,
        "high_priority_comments": 7
      },
      "form_completion_metrics": {
        "consent_assent": {
          "completion_rate": 98.5,
          "avg_completion_time_minutes": 15
        },
        "registration_rapid_assessment": {
          "completion_rate": 94.2,
          "avg_completion_time_minutes": 45
        }
      }
    }
  }
}
```

### Case Manager Metrics

```http
GET /api/v1/dashboard/case_metrics?assigned_user_id=user_789
Authorization: Bearer <token>
```

## Lookup Data Endpoints

### Geographic Locations

```http
GET /api/v1/geographic_locations?filter[country]=Syria&include=children
Authorization: Bearer <token>
```

### Service Categories

```http
GET /api/v1/service_categories?filter[active]=true
Authorization: Bearer <token>
```

### Referral Sources

```http
GET /api/v1/referral_sources?sort=name
Authorization: Bearer <token>
```

## Real-time Features

### WebSocket Connection

```javascript
// Connect to case updates
const socket = new WebSocket("wss://api.athar.org/cable");

// Subscribe to case updates
socket.send(
  JSON.stringify({
    command: "subscribe",
    identifier: JSON.stringify({
      channel: "CaseChannel",
      case_id: "case_123",
    }),
  })
);

// Listen for updates
socket.onmessage = function (event) {
  const data = JSON.parse(event.data);
  if (data.type === "case_updated") {
    // Handle case update
    console.log("Case updated:", data.case);
  }
};
```

### Real-time Comment Notifications

```javascript
// Subscribe to comment updates for a case
socket.send(
  JSON.stringify({
    command: "subscribe",
    identifier: JSON.stringify({
      channel: "CommentChannel",
      case_id: "case_123",
    }),
  })
);
```

## File Upload Support

### Upload Case Documents

```http
POST /api/v1/cases/123/documents
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "document": <file_binary>,
  "document_type": "identification",
  "description": "Child's birth certificate"
}
```

**Response:**

```json
{
  "data": {
    "id": "doc_456",
    "type": "document",
    "attributes": {
      "filename": "birth_certificate.pdf",
      "document_type": "identification",
      "file_size": 245760,
      "uploaded_at": "2025-01-20T10:00:00Z",
      "download_url": "/api/v1/documents/doc_456/download"
    }
  }
}
```

## Bulk Operations

### Bulk Case Assignment

```http
POST /api/v1/cases/bulk_assign
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "bulk_assignment",
    "attributes": {
      "case_ids": ["case_123", "case_124", "case_125"],
      "assigned_user_id": "user_789",
      "reason": "Workload redistribution"
    }
  }
}
```

### Bulk Status Update

```http
PATCH /api/v1/cases/bulk_update
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "bulk_update",
    "attributes": {
      "case_ids": ["case_123", "case_124"],
      "updates": {
        "status": "in_progress",
        "priority_level": "high"
      }
    }
  }
}
```

## Webhooks (Future Enhancement)

The system will support webhooks for real-time notifications:

- Case status changes
- Form completions
- Approval decisions
- Comment additions

### Webhook Registration

```http
POST /api/v1/webhooks
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "webhook",
    "attributes": {
      "url": "https://external-system.org/webhooks/case-updates",
      "events": ["case.status_changed", "case.approved", "comment.created"],
      "secret": "webhook_secret_key"
    }
  }
}
```
