puts "🔍 Checking for Avatar-related Fields"
puts "=" * 50

user = RemoteUser.new(id: 1)
user_data = user.send(:fetch_user_data)

if user_data
  # Try common avatar field names
  avatar_fields = [
    'avatar_url', 'avatar', 'avatar_image', 'avatar_path',
    'profile_image', 'profile_picture', 'image_url', 'photo_url',
    'picture', 'image', 'photo'
  ]
  
  puts "🔍 Checking for avatar fields:"
  avatar_fields.each do |field|
    begin
      if user_data.respond_to?(field)
        value = user_data.send(field)
        puts "   ✅ #{field}: #{value.inspect}"
      else
        puts "   ❌ #{field}: not available"
      end
    rescue => e
      puts "   ⚠️  #{field}: ERROR - #{e.message}"
    end
  end
  
  puts "\n📋 All available fields from to_hash:"
  hash_data = user_data.to_hash
  hash_data.each do |key, value|
    puts "   - #{key}: #{value.class} = #{value.inspect}"
  end
  
else
  puts "❌ No user data available"
end
