puts "🌱 Creating Sample Services Catalog..."

# Services are now a generic catalog, not tied to specific cases
# Project 3 (assuming it exists from existing seeds)
project_id = 3

# Service types that can be provided to beneficiaries
service_types = [
  {
    name: "Protection Services",
    description: "Comprehensive protection support and monitoring for vulnerable beneficiaries",
    category: "social",
    provider_type: "internal",
    project_id: project_id
  },
  {
    name: "Educational Support",
    description: "School enrollment assistance and educational materials provision",
    category: "education",
    provider_type: "external",
    project_id: project_id
  },
  {
    name: "Healthcare Services",
    description: "Medical care coordination and health monitoring services",
    category: "health",
    provider_type: "external",
    project_id: project_id
  },
  {
    name: "Psychosocial Support",
    description: "Counseling and emotional support services for trauma recovery",
    category: "social",
    provider_type: "internal",
    project_id: project_id
  },
  {
    name: "Family Reunification Support",
    description: "Support for family tracing and reunification processes",
    category: "social",
    provider_type: "internal",
    project_id: project_id
  },
  {
    name: "Legal Assistance",
    description: "Legal aid and documentation support services",
    category: "legal",
    provider_type: "external",
    project_id: project_id
  },
  {
    name: "Livelihood Support",
    description: "Vocational training and employment assistance programs",
    category: "employment",
    provider_type: "external",
    project_id: project_id
  },
  {
    name: "Emergency Housing",
    description: "Temporary accommodation and housing assistance",
    category: "housing",
    provider_type: "external",
    project_id: project_id
  },
  {
    name: "Financial Assistance",
    description: "Emergency cash assistance and financial support",
    category: "financial",
    provider_type: "internal",
    project_id: project_id
  }
]

# Create generic services catalog
service_types.each do |service_data|
  # Check if service already exists to avoid duplicates
  existing_service = Service.find_by(name: service_data[:name], category: service_data[:category])

  if existing_service
    puts "  ⚠️  Service already exists: #{service_data[:name]}"
  else
    service = Service.create!(service_data)
    puts "  ✅ Created service: #{service.name} (#{service.category})"
  end
rescue ActiveRecord::RecordInvalid => e
  puts "  ❌ Failed to create service #{service_data[:name]}: #{e.message}"
end

total_services = Service.count
puts "🌱 Sample Services catalog creation completed! Total services: #{total_services}"
