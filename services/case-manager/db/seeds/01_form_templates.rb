# Form Templates for Humanitarian Case Management
# Based on Primero.org standards and UNHCR guidelines

puts "🌱 Creating Form Templates for Case Management..."

# Project 1 (assuming it exists from existing seeds)
project_id = 3

# 1. Consent and Assent Form Template
consent_template = FormTemplate.find_or_create_by!(
  name: 'consent_assent',
  project_id: project_id
) do |template|
  template.title = 'Consent and Assent Form'
  template.description = 'Initial consent and assent collection for case management services'

  template.active = true
  template.sequence_order = 1
  template.prerequisite_forms = []
  template.target_role = 'both'
end

# Consent Form Sections
consent_section = FormSection.find_or_create_by!(
  form_template: consent_template,
  name: 'consent_information'
) do |section|
  section.title = 'Consent Information'
  section.description = 'Basic consent and assent collection'
  section.display_order = 1
  section.is_required = true
end

# Consent Form Fields
consent_fields = [
  {
    name: 'consent_given',
    label: 'Consent Given',
    field_type: 'checkbox',
    is_required: true,
    help_text: 'Has the beneficiary or guardian provided consent for case management services?'
  },
  {
    name: 'consent_date',
    label: 'Date of Consent',
    field_type: 'date',
    is_required: true,
    help_text: 'Date when consent was provided'
  },
  {
    name: 'consent_given_by',
    label: 'Consent Given By',
    field_type: 'select_field',
    is_required: true,
    help_text: 'Who provided the consent?'
  },
  {
    name: 'assent_applicable',
    label: 'Is Assent Applicable?',
    field_type: 'checkbox',
    is_required: true,
    help_text: 'Is the beneficiary a minor who can provide assent?'
  },
  {
    name: 'assent_given',
    label: 'Assent Given',
    field_type: 'checkbox',
    is_required: false,
    help_text: 'Has the minor provided assent for services?'
  },
  {
    name: 'interpreter_used',
    label: 'Interpreter Used',
    field_type: 'checkbox',
    is_required: true,
    help_text: 'Was an interpreter used during consent process?'
  },
  {
    name: 'interpreter_language',
    label: 'Interpreter Language',
    field_type: 'text',
    is_required: false,
    help_text: 'Language used by interpreter'
  }
]

consent_fields.each_with_index do |field_data, index|
  field = FormField.find_or_create_by!(
    form_section: consent_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean_data' :
                      field_data[:field_type] == 'date' ? 'date_data' :
                      field_data[:field_type] == 'datetime' ? 'datetime_data' :
                      field_data[:field_type] == 'number' ? 'integer_data' : 'string_data'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end

  # Add options for select fields
  if field_data[:name] == 'consent_given_by'
    options = ['Beneficiary', 'Parent/Guardian', 'Legal Representative', 'Other']
    options.each_with_index do |option, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: option.downcase.gsub(/[^a-z0-9]/, '_'),
        option_value: option,
        display_order: opt_index + 1
      )
    end
  end
end

puts "✅ Created Consent and Assent Form Template"

# 2. Registration and Rapid Assessment Form Template
registration_template = FormTemplate.find_or_create_by!(
  name: 'registration_rapid_assessment',
  project_id: project_id
) do |template|
  template.title = 'Registration and Rapid Assessment'
  template.description = 'Initial registration and rapid needs assessment'

  template.active = true
  template.sequence_order = 2
  template.prerequisite_forms = ['consent_assent']
  template.target_role = 'both'
end

# Registration Sections
personal_info_section = FormSection.find_or_create_by!(
  form_template: registration_template,
  name: 'personal_information'
) do |section|
  section.title = 'Personal Information'
  section.description = 'Basic personal and demographic information'
  section.display_order = 1
  section.is_required = true
end

# Personal Information Fields
personal_fields = [
  {
    name: 'full_name',
    label: 'Full Name',
    field_type: 'text',
    is_required: true,
    help_text: 'Complete name of the beneficiary'
  },
  {
    name: 'date_of_birth',
    label: 'Date of Birth',
    field_type: 'date',
    is_required: true,
    help_text: 'Date of birth (if unknown, provide estimated date)'
  },
  {
    name: 'age',
    label: 'Age',
    field_type: 'number',
    is_required: true,
    help_text: 'Current age in years'
  },
  {
    name: 'gender',
    label: 'Gender',
    field_type: 'select_field',
    is_required: true,
    help_text: 'Gender identity of the beneficiary'
  },
  {
    name: 'nationality',
    label: 'Nationality',
    field_type: 'text',
    is_required: true,
    help_text: 'Country of nationality'
  },
  {
    name: 'country_of_origin',
    label: 'Country of Origin',
    field_type: 'text',
    is_required: true,
    help_text: 'Country where the person originated from'
  },
  {
    name: 'languages_spoken',
    label: 'Languages Spoken',
    field_type: 'textarea',
    is_required: true,
    help_text: 'List all languages spoken by the beneficiary'
  },
  {
    name: 'primary_language',
    label: 'Primary Language',
    field_type: 'text',
    is_required: true,
    help_text: 'Main language used for communication'
  }
]

personal_fields.each_with_index do |field_data, index|
  field = FormField.find_or_create_by!(
    form_section: personal_info_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean_data' :
                      field_data[:field_type] == 'date' ? 'date_data' :
                      field_data[:field_type] == 'datetime' ? 'datetime_data' :
                      field_data[:field_type] == 'number' ? 'integer_data' : 'string_data'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end

  # Add options for gender field
  if field_data[:name] == 'gender'
    options = ['Male', 'Female', 'Non-binary', 'Prefer not to say', 'Other']
    options.each_with_index do |option, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: option.downcase.gsub(/[^a-z0-9]/, '_'),
        option_value: option,
        display_order: opt_index + 1
      )
    end
  end
end

puts "✅ Created Registration and Rapid Assessment Form Template"

puts "🌱 Form Templates creation completed!"
