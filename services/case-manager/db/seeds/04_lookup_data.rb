# Lookup Data Seeds
# Create initial lookup data for all lookup models

puts "🌱 Creating Lookup Data..."

# Project 1 (assuming it exists from existing seeds)
project_id = 3

# Staff Positions
puts "Creating Staff Positions..."
staff_positions = [
  { name: 'Case Manager', name_ar: 'مدير الحالة', code: 'CM', department: 'Protection' },
  { name: 'Social Worker', name_ar: 'أخصائي اجتماعي', code: 'SW', department: 'Protection' },
  { name: 'Supervisor', name_ar: 'مشرف', code: 'SUP', department: 'Protection' },
  { name: 'Program Manager', name_ar: 'مدير البرنامج', code: 'PM', department: 'Management' },
  { name: 'Field Officer', name_ar: 'ضابط ميداني', code: 'FO', department: 'Operations' },
  { name: 'Psychologist', name_ar: 'أخصائي نفسي', code: 'PSY', department: 'Mental Health' },
  { name: 'Legal Advisor', name_ar: 'مستشار قانوني', code: 'LA', department: 'Legal' },
  { name: 'Community Mobilizer', name_ar: 'محرك مجتمعي', code: 'CM', department: 'Community' }
]

staff_positions.each_with_index do |position_data, index|
  StaffPosition.find_or_create_by!(
    code: position_data[:code],
    project_id: project_id
  ) do |position|
    position.name = position_data[:name]
    position.name_ar = position_data[:name_ar]
    position.department = position_data[:department]
    position.display_order = index + 1
    position.active = true
  end
end

# Partner Agencies
puts "Creating Partner Agencies..."
partner_agencies = [
  { name: 'UNICEF', name_ar: 'يونيسف', agency_type: 'un_agency', contact_email: '<EMAIL>' },
  { name: 'UNHCR', name_ar: 'مفوضية اللاجئين', agency_type: 'un_agency', contact_email: '<EMAIL>' },
  { name: 'Save the Children', name_ar: 'أنقذوا الأطفال', agency_type: 'ingo', contact_email: '<EMAIL>' },
  { name: 'World Vision', name_ar: 'الرؤية العالمية', agency_type: 'ingo', contact_email: '<EMAIL>' },
  { name: 'Ministry of Social Affairs', name_ar: 'وزارة الشؤون الاجتماعية', agency_type: 'government', contact_email: '<EMAIL>' },
  { name: 'Local Community Center', name_ar: 'المركز المجتمعي المحلي', agency_type: 'local_org', contact_email: '<EMAIL>' },
  { name: 'Red Cross', name_ar: 'الصليب الأحمر', agency_type: 'ingo', contact_email: '<EMAIL>' },
  { name: 'Caritas', name_ar: 'كاريتاس', agency_type: 'ngo', contact_email: '<EMAIL>' }
]

partner_agencies.each_with_index do |agency_data, index|
  PartnerAgency.find_or_create_by!(
    name: agency_data[:name],
    project_id: project_id
  ) do |agency|
    agency.name_ar = agency_data[:name_ar]
    agency.agency_type = agency_data[:agency_type]
    agency.contact_email = agency_data[:contact_email]
    agency.display_order = index + 1
    agency.active = true
  end
end

# Geographic Locations (Hierarchical)
puts "Creating Geographic Locations..."

# Countries
countries = [
  { name: 'Jordan', name_ar: 'الأردن', location_type: 'country', iso_code: 'JO' },
  { name: 'Lebanon', name_ar: 'لبنان', location_type: 'country', iso_code: 'LB' },
  { name: 'Syria', name_ar: 'سوريا', location_type: 'country', iso_code: 'SY' }
]

countries.each_with_index do |country_data, index|
  GeographicLocation.find_or_create_by!(
    name: country_data[:name],
    location_type: 'country',
    project_id: project_id
  ) do |location|
    location.name_ar = country_data[:name_ar]
    location.iso_code = country_data[:iso_code]
    location.display_order = index + 1
    location.active = true
  end
end

# Governorates for Jordan
jordan = GeographicLocation.find_by(name: 'Jordan', location_type: 'country', project_id: project_id)
if jordan
  governorates = [
    { name: 'Amman', name_ar: 'عمان', location_type: 'governorate' },
    { name: 'Irbid', name_ar: 'إربد', location_type: 'governorate' },
    { name: 'Zarqa', name_ar: 'الزرقاء', location_type: 'governorate' },
    { name: 'Mafraq', name_ar: 'المفرق', location_type: 'governorate' }
  ]

  governorates.each_with_index do |gov_data, index|
    GeographicLocation.find_or_create_by!(
      name: gov_data[:name],
      location_type: 'governorate',
      parent_location: jordan,
      project_id: project_id
    ) do |location|
      location.name_ar = gov_data[:name_ar]
      location.display_order = index + 1
      location.active = true
    end
  end
end

# Case Types
puts "Creating Case Types..."
case_types = [
  { name: 'Child Protection', name_ar: 'حماية الطفل', category: 'child_protection' },
  { name: 'Gender-Based Violence', name_ar: 'العنف القائم على النوع الاجتماعي', category: 'gbv' },
  { name: 'General Protection', name_ar: 'الحماية العامة', category: 'general_protection' },
  { name: 'Unaccompanied Minor', name_ar: 'قاصر غير مصحوب', category: 'child_protection' },
  { name: 'Family Separation', name_ar: 'انفصال الأسرة', category: 'child_protection' },
  { name: 'Domestic Violence', name_ar: 'العنف المنزلي', category: 'gbv' }
]

case_types.each_with_index do |type_data, index|
  CaseType.find_or_create_by!(
    name: type_data[:name],
    project_id: project_id
  ) do |case_type|
    case_type.name_ar = type_data[:name_ar]
    case_type.category = type_data[:category]
    case_type.display_order = index + 1
    case_type.active = true
  end
end

# Service Categories
puts "Creating Service Categories..."
service_categories = [
  { name: 'Legal Assistance', name_ar: 'المساعدة القانونية', sector: 'legal', target_group: 'all_groups' },
  { name: 'Medical Care', name_ar: 'الرعاية الطبية', sector: 'health', target_group: 'all_groups' },
  { name: 'Mental Health Support', name_ar: 'دعم الصحة النفسية', sector: 'health', target_group: 'all_groups' },
  { name: 'Educational Support', name_ar: 'الدعم التعليمي', sector: 'education', target_group: 'children' },
  { name: 'Livelihood Training', name_ar: 'تدريب سبل العيش', sector: 'employment', target_group: 'all_groups' },
  { name: 'Emergency Assistance', name_ar: 'المساعدة الطارئة', sector: 'social', target_group: 'all_groups' },
  { name: 'Family Tracing', name_ar: 'تتبع الأسرة', sector: 'social', target_group: 'children' },
  { name: 'Documentation Support', name_ar: 'دعم التوثيق', sector: 'legal', target_group: 'all_groups' },
  { name: 'Shelter Support', name_ar: 'دعم المأوى', sector: 'housing', target_group: 'all_groups' },
  { name: 'Food Assistance', name_ar: 'المساعدة الغذائية', sector: 'social', target_group: 'all_groups' },
  { name: 'Cash Assistance', name_ar: 'المساعدة النقدية', sector: 'financial', target_group: 'all_groups' },
  { name: 'Vocational Training', name_ar: 'التدريب المهني', sector: 'employment', target_group: 'all_groups' }
]

service_categories.each_with_index do |category_data, index|
  ServiceCategory.find_or_create_by!(
    name: category_data[:name],
    project_id: project_id
  ) do |category|
    category.name_ar = category_data[:name_ar]
    category.sector = category_data[:sector]
    category.target_group = category_data[:target_group]
    category.display_order = index + 1
    category.active = true
  end
end

# Referral Sources
puts "Creating Referral Sources..."
referral_sources = [
  { name: 'Self Referral', name_ar: 'إحالة ذاتية', source_type: 'self_referral' },
  { name: 'Community Member', name_ar: 'عضو مجتمع', source_type: 'community_member' },
  { name: 'Partner Organization', name_ar: 'منظمة شريكة', source_type: 'organization' },
  { name: 'Government Agency', name_ar: 'وكالة حكومية', source_type: 'government' },
  { name: 'Hotline', name_ar: 'الخط الساخن', source_type: 'hotline' },
  { name: 'School', name_ar: 'مدرسة', source_type: 'organization' },
  { name: 'Health Center', name_ar: 'مركز صحي', source_type: 'organization' },
  { name: 'Police', name_ar: 'شرطة', source_type: 'government' }
]

referral_sources.each_with_index do |source_data, index|
  ReferralSource.find_or_create_by!(
    name: source_data[:name],
    project_id: project_id
  ) do |source|
    source.name_ar = source_data[:name_ar]
    source.source_type = source_data[:source_type]
    source.display_order = index + 1
    source.active = true
  end
end

puts "✅ Lookup data created successfully!"
puts "   - #{StaffPosition.count} Staff Positions"
puts "   - #{PartnerAgency.count} Partner Agencies"
puts "   - #{GeographicLocation.count} Geographic Locations"
puts "   - #{CaseType.count} Case Types"
puts "   - #{ServiceCategory.count} Service Categories"
puts "   - #{ReferralSource.count} Referral Sources"
