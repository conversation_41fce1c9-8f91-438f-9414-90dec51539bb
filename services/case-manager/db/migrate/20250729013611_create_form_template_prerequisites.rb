class CreateFormTemplatePrerequisites < ActiveRecord::Migration[8.0]
  def change
    create_table :form_template_prerequisites do |t|
      t.references :form_template, null: false, foreign_key: true
      t.references :prerequisite_template, null: false, foreign_key: { to_table: :form_templates }

      t.timestamps
    end

    add_index :form_template_prerequisites, [ :form_template_id, :prerequisite_template_id ],
              unique: true, name: 'index_form_template_prerequisites_unique'
  end
end
