class RemoveBeneficiaryCacheFromCases < ActiveRecord::Migration[8.0]
  def up
    # Remove the redundant beneficiary_cache JSON column
    # Individual columns (beneficiary_name, beneficiary_age, etc.) are kept for performance
    remove_column :cases, :beneficiary_cache, :json
  end

  def down
    # Add the column back if rollback is needed
    add_column :cases, :beneficiary_cache, :json, default: {}
  end
end
