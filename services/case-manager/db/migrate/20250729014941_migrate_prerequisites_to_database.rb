class MigratePrerequisitesToDatabase < ActiveRecord::Migration[8.0]
  def up
    # Migrate existing prerequisite_forms JSON data to FormTemplatePrerequisite relationships
    FormTemplate.find_each do |template|
      next if template.prerequisite_forms.blank?

      template.prerequisite_forms.each do |prereq_name|
        prereq_template = FormTemplate.find_by(name: prereq_name, project_id: template.project_id)
        next unless prereq_template

        # Create prerequisite relationship if it doesn't exist
        FormTemplatePrerequisite.find_or_create_by(
          form_template: template,
          prerequisite_template: prereq_template
        )
      end
    end

    puts "Migrated prerequisites for #{FormTemplatePrerequisite.count} relationships"
  end

  def down
    # Remove all prerequisite relationships (data will be preserved in prerequisite_forms JSON)
    FormTemplatePrerequisite.delete_all
    puts "Removed all FormTemplatePrerequisite relationships"
  end
end
