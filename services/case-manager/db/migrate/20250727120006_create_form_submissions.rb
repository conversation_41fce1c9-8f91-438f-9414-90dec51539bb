class CreateFormSubmissions < ActiveRecord::Migration[8.0]
  def change
    create_table :form_submissions do |t|
      t.uuid :case_id, null: false
      t.references :form_template, null: false, foreign_key: true
      t.uuid :created_by_id, null: false
      t.uuid :updated_by_id
      t.integer :status, default: 0
      t.json :form_data, default: {}
      t.json :completion_status, default: {}
      t.datetime :submitted_at
      t.datetime :completed_at

      t.timestamps
    end

    add_index :form_submissions, :case_id
    add_index :form_submissions, :status
    add_index :form_submissions, :created_by_id
    add_index :form_submissions, :submitted_at
    add_index :form_submissions, :completed_at
    add_index :form_submissions, [ :case_id, :form_template_id ], unique: true
  end
end
