class RemoveFormTemplateFromSubmissions < ActiveRecord::Migration[8.0]
  def up
    # Step 1: Ensure all submissions have form_section_id
    # Find submissions without form_section_id and assign them to first section of their template
    puts "Migrating submissions without form_section_id..."
    
    submissions_without_section = execute(
      "SELECT id, form_template_id FROM form_submissions WHERE form_section_id IS NULL"
    )
    
    submissions_without_section.each do |row|
      submission_id = row['id']
      template_id = row['form_template_id']
      
      # Find first section of this template
      first_section = execute(
        "SELECT id FROM form_sections WHERE form_template_id = #{template_id} ORDER BY display_order LIMIT 1"
      ).first
      
      if first_section
        execute(
          "UPDATE form_submissions SET form_section_id = #{first_section['id']} WHERE id = #{submission_id}"
        )
        puts "Migrated submission #{submission_id} to section #{first_section['id']}"
      else
        puts "WARNING: No sections found for template #{template_id}, submission #{submission_id}"
      end
    end

    # Step 2: Remove the unique constraint that includes form_template_id
    remove_index :form_submissions, name: "index_form_submissions_on_case_id_and_form_template_id"

    # Step 3: Make form_section_id NOT NULL
    change_column_null :form_submissions, :form_section_id, false

    # Step 4: Remove foreign key constraint first
    remove_foreign_key :form_submissions, :form_templates

    # Step 5: Remove form_template_id column
    remove_column :form_submissions, :form_template_id, :bigint

    # Step 6: Add new unique constraint on case_id and form_section_id
    add_index :form_submissions, [:case_id, :form_section_id], 
              unique: true, 
              name: "index_form_submissions_on_case_and_section"

    puts "Migration completed successfully!"
  end

  def down
    # Reverse migration
    puts "Reversing migration..."
    
    # Add form_template_id column back
    add_column :form_submissions, :form_template_id, :bigint
    
    # Populate form_template_id from form_section
    execute(
      "UPDATE form_submissions 
       SET form_template_id = form_sections.form_template_id 
       FROM form_sections 
       WHERE form_submissions.form_section_id = form_sections.id"
    )

    # Restore constraints
    remove_index :form_submissions, name: "index_form_submissions_on_case_and_section"
    
    change_column_null :form_submissions, :form_section_id, true
    add_index :form_submissions, [:case_id, :form_template_id], 
              unique: true, 
              name: "index_form_submissions_on_case_id_and_form_template_id"
    
    add_foreign_key :form_submissions, :form_templates
    
    puts "Rollback completed!"
  end
end
