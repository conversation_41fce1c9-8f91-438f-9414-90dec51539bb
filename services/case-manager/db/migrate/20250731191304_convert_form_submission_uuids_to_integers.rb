class ConvertFormSubmissionUuidsToIntegers < ActiveRecord::Migration[8.0]
  def up
    # Convert UUID columns to integers in form_submissions table
    change_column :form_submissions, :case_id, :integer, using: "SUBSTRING(case_id::text FROM '550e8400-e29b-41d4-a716-44665544000(.+)')::integer"
    change_column :form_submissions, :created_by_id, :integer, using: "SUBSTRING(created_by_id::text FROM '550e8400-e29b-41d4-a716-44665544000(.+)')::integer"
    change_column :form_submissions, :updated_by_id, :integer, using: "SUBSTRING(updated_by_id::text FROM '550e8400-e29b-41d4-a716-44665544000(.+)')::integer"
  end

  def down
    # Convert back to UUIDs using the original seed pattern
    change_column :form_submissions, :case_id, :uuid, using: "('550e8400-e29b-41d4-a716-44665544000' || case_id::text)::uuid"
    change_column :form_submissions, :created_by_id, :uuid, using: "('550e8400-e29b-41d4-a716-44665544000' || created_by_id::text)::uuid"
    change_column :form_submissions, :updated_by_id, :uuid, using: "('550e8400-e29b-41d4-a716-44665544000' || updated_by_id::text)::uuid"
  end
end
