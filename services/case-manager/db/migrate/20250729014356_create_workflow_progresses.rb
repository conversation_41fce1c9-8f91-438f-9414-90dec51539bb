class CreateWorkflowProgresses < ActiveRecord::Migration[8.0]
  def change
    create_table :workflow_progresses do |t|
      t.uuid :case_id, null: false
      t.references :form_template, null: false, foreign_key: true
      t.decimal :completion_percentage, precision: 5, scale: 2, default: 0.0
      t.integer :sections_completed, default: 0
      t.integer :total_sections, default: 0
      t.datetime :last_updated_at
      t.json :section_progress, default: {}

      t.timestamps
    end

    add_index :workflow_progresses, [ :case_id, :form_template_id ], unique: true
    add_index :workflow_progresses, :case_id
    add_index :workflow_progresses, :completion_percentage
    add_index :workflow_progresses, :last_updated_at
  end
end
