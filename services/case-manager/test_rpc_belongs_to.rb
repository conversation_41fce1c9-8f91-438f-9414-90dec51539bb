puts "🧪 Testing rpc_belongs_to functionality"
puts "=" * 50

# Test 1: Create a test model with rpc_belongs_to
class TestModel < ApplicationRecord
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  self.table_name = 'cases'
  
  rpc_belongs_to :assigned_user, foreign_key: :assigned_user_id, class_name: 'RemoteUser'
  rpc_belongs_to :created_by_user, foreign_key: :created_by_id, class_name: 'RemoteUser'
end

puts "✅ TestModel created with rpc_belongs_to associations"

# Test 2: Test with valid user ID
begin
  puts "\n--- Testing with valid user ID ---"
  test_case = TestModel.new(assigned_user_id: 1)
  user = test_case.assigned_user
  puts "✅ User fetched: #{user.name} (#{user.email})"
rescue => e
  puts "❌ Valid user test failed: #{e.message}"
end

# Test 3: Test with nil user ID
begin
  puts "\n--- Testing with nil user ID ---"
  test_case_nil = TestModel.new(assigned_user_id: nil)
  user_nil = test_case_nil.assigned_user
  puts "✅ Nil user handled correctly: #{user_nil.inspect}"
rescue => e
  puts "❌ Nil user test failed: #{e.message}"
end

# Test 4: Test assignment
begin
  puts "\n--- Testing user assignment ---"
  test_case = TestModel.new
  remote_user = RemoteUser.new(id: 1, name: 'Test User')
  test_case.assigned_user = remote_user
  puts "✅ User assigned: foreign_key=#{test_case.assigned_user_id}"
  puts "✅ Retrieved user: #{test_case.assigned_user.name}"
rescue => e
  puts "❌ Assignment test failed: #{e.message}"
end

# Test 5: Test build method
begin
  puts "\n--- Testing build method ---"
  test_case = TestModel.new
  built_user = test_case.build_assigned_user(id: 1, name: 'Built User')
  puts "✅ User built: #{built_user.name}"
  puts "✅ Foreign key set: #{test_case.assigned_user_id}"
rescue => e
  puts "❌ Build test failed: #{e.message}"
end

puts "\n" + "=" * 50
puts "🎉 rpc_belongs_to tests complete!"
puts "✅ New method name working correctly"
puts "✅ All functionality preserved"
puts "=" * 50
