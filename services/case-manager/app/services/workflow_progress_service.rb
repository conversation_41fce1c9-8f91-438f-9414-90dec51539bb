class WorkflowProgressService
  include ActiveModel::Model

  attr_accessor :case_id

  def initialize(case_id:)
    @case_id = case_id
  end

  # Calculate progress for all templates for a case
  def calculate_all_progress
    case_obj = Case.find(case_id)
    templates = FormTemplate.workflow_templates.for_project(case_obj.project_id)
    
    results = {}
    
    templates.find_each do |template|
      progress = WorkflowProgress.calculate_for_case_and_template(case_id, template)
      results[template.id] = progress.progress_summary
    end
    
    # Trigger real-time update
    broadcast_progress_update(results)
    
    results
  end

  # Calculate progress for a specific template
  def calculate_template_progress(template_id)
    template = FormTemplate.find(template_id)
    progress = WorkflowProgress.calculate_for_case_and_template(case_id, template)
    
    result = progress.progress_summary
    
    # Trigger real-time update for this template
    broadcast_template_progress_update(template_id, result)
    
    result
  end

  # Get current progress summary for a case
  def current_progress_summary
    progresses = WorkflowProgress.for_case(case_id).includes(:form_template)
    
    {
      case_id: case_id,
      overall_completion: calculate_overall_completion(progresses),
      templates: progresses.map(&:progress_summary),
      last_updated_at: progresses.maximum(:last_updated_at),
      next_steps: determine_next_steps(progresses)
    }
  end

  # Refresh progress when form data changes
  def refresh_progress_for_template(template_id)
    template = FormTemplate.find(template_id)
    progress = WorkflowProgress.find_by(case_id: case_id, form_template: template)
    
    if progress
      progress.recalculate!
      broadcast_template_progress_update(template_id, progress.progress_summary)
      progress.progress_summary
    else
      calculate_template_progress(template_id)
    end
  end

  # Background job trigger
  def self.refresh_case_progress_async(case_id)
    RefreshWorkflowProgressJob.perform_later(case_id)
  end

  private

  def calculate_overall_completion(progresses)
    return 0.0 if progresses.empty?
    
    total_percentage = progresses.sum(&:completion_percentage)
    (total_percentage / progresses.count).round(2)
  end

  def determine_next_steps(progresses)
    # Find the next template that should be worked on
    case_obj = Case.find(case_id)
    all_templates = FormTemplate.workflow_templates
                                .for_project(case_obj.project_id)
                                .ordered

    next_steps = []
    
    all_templates.each do |template|
      progress = progresses.find { |p| p.form_template_id == template.id }
      
      if progress.nil? || progress.not_started?
        # Check if prerequisites are met
        if template.prerequisites_met?(case_obj)
          next_steps << {
            template_id: template.id,
            template_name: template.name,
            template_title: template.title,
            action: 'start',
            priority: 'high'
          }
          break # Return first available next step
        else
          next_steps << {
            template_id: template.id,
            template_name: template.name,
            template_title: template.title,
            action: 'prerequisites_required',
            priority: 'blocked',
            missing_prerequisites: get_missing_prerequisites(template, case_obj)
          }
        end
      elsif progress.in_progress?
        next_steps << {
          template_id: template.id,
          template_name: template.name,
          template_title: template.title,
          action: 'continue',
          priority: 'medium',
          completion_percentage: progress.completion_percentage
        }
      end
    end
    
    next_steps
  end

  def get_missing_prerequisites(template, case_obj)
    if template.form_template_prerequisites.any?
      template.prerequisite_templates.reject do |prereq|
        submission = case_obj.form_submissions.find_by(form_template: prereq)
        submission&.form_completed?
      end.map do |prereq|
        {
          id: prereq.id,
          name: prereq.name,
          title: prereq.title
        }
      end
    else
      # Legacy system
      template.prerequisite_forms.reject do |prereq_name|
        prereq_template = FormTemplate.find_by(name: prereq_name, project_id: template.project_id)
        next true unless prereq_template
        
        submission = case_obj.form_submissions.find_by(form_template: prereq_template)
        submission&.form_completed?
      end.map do |prereq_name|
        prereq_template = FormTemplate.find_by(name: prereq_name, project_id: template.project_id)
        {
          id: prereq_template&.id,
          name: prereq_name,
          title: prereq_template&.title
        }
      end.compact
    end
  end

  def broadcast_progress_update(results)
    # ActionCable broadcast for real-time updates
    ActionCable.server.broadcast(
      "case_progress_#{case_id}",
      {
        type: 'progress_update',
        case_id: case_id,
        data: results,
        timestamp: Time.current.iso8601
      }
    )
  end

  def broadcast_template_progress_update(template_id, result)
    # ActionCable broadcast for specific template updates
    ActionCable.server.broadcast(
      "case_progress_#{case_id}",
      {
        type: 'template_progress_update',
        case_id: case_id,
        template_id: template_id,
        data: result,
        timestamp: Time.current.iso8601
      }
    )
  end
end
