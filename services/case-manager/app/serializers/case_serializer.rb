class CaseSerializer
  include JSONAPI::Serializer

  # Basic case attributes
  attributes :status, :approval_status,
             :started_at, :closed_at, :created_at, :updated_at

  # Case metadata
  attributes :case_number, :case_type, :priority_level,
             :confidentiality_level, :notes

  # Beneficiary information
  attributes :beneficiary_name, :beneficiary_age, :beneficiary_gender,
             :beneficiary_nationality, :beneficiary_phone, :beneficiary_id_number,
             :beneficiary_date_of_birth, :beneficiary_city

  # Computed attributes
  attribute :age_in_days do |case_record|
    case_record.age_in_days
  end

  attribute :is_active do |case_record|
    case_record.is_active?
  end

  attribute :is_closed do |case_record|
    case_record.is_closed?
  end

  attribute :is_suspended do |case_record|
    case_record.is_suspended?
  end

  attribute :requires_approval do |case_record|
    case_record.requires_approval?
  end

  attribute :is_approved do |case_record|
    case_record.is_approved?
  end

  attribute :is_rejected do |case_record|
    case_record.is_rejected?
  end

  attribute :can_be_closed do |case_record|
    case_record.can_be_closed?
  end

  # Relationships
  belongs_to :assigned_user, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :assigned_user_id
  belongs_to :created_by_user, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :created_by_id
  belongs_to :project, serializer: ProjectSerializer, id_method_name: :project_id
end
