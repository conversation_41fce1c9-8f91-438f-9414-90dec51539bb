class FormFieldOptionSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :option_key, :option_value, :display_order, :active, :metadata,
             :created_at, :updated_at

  # Computed attributes
  attribute :is_active do |object|
    object.active?
  end

  # Form field relationship
  attribute :form_field_id do |object|
    object.form_field_id
  end

  attribute :form_field_name do |object|
    object.form_field&.field_name
  end

  attribute :form_field_label do |object|
    object.form_field&.label
  end

  # Relationships
  belongs_to :form_field
end
