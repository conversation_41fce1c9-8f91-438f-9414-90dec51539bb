# frozen_string_literal: true

# Serializer for RemoteUser instances
# Handles serialization of user data fetched from Core service via GRPC
class RemoteUserSerializer
  include JSONAPI::Serializer

  set_type :remote_user

  attributes :id, :name, :email, :status, :avatar_url

  # Include user roles information from GRPC response
  attribute :user_roles do |object|
    user_data = object.send(:fetch_user_data)
    if user_data&.respond_to?(:user_roles_list)
      user_data.user_roles_list.map do |user_role|
        {
          id: user_role.id,
          role: {
            id: user_role.role.id,
            name: user_role.role.name,
            global: user_role.role.global,
            level: user_role.role.level
          },
          project: {
            id: user_role.project.id,
            name: user_role.project.name,
            description: user_role.project.description,
            status: user_role.project.status
          },
          is_default: user_role.is_default
        }
      end
    else
      []
    end
  end
end
