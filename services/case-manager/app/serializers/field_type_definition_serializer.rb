class FieldTypeDefinitionSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :name, :input_type, :data_type, :description, :validation_schema,
             :default_config, :active, :version, :created_at, :updated_at

  # Computed attributes
  attribute :is_active do |object|
    object.active?
  end

  attribute :data_type_display do |object|
    object.data_type&.humanize
  end

  attribute :input_type_display do |object|
    object.input_type&.humanize
  end

  attribute :supports_validation do |object|
    ->(validation_type) { object.supports_validation?(validation_type) }
  end

  attribute :default_validation_rules do |object|
    object.default_validation_rules
  end

  attribute :form_fields_count do |object|
    object.form_fields.count
  end

  # Relationships
  has_many :form_fields
end
