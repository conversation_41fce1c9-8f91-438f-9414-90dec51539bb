class FormFieldSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :field_name, :label, :field_type, :data_type, :display_order, 
             :required, :visible, :help_text, :placeholder, :validation_rules,
             :field_config, :calculation_formula, :lookup_source_type, 
             :lookup_source_config, :parent_field_id, :depends_on, 
             :auto_calculate, :created_at, :updated_at

  # Computed attributes
  attribute :is_required do |object|
    object.required?
  end

  attribute :is_visible do |object|
    object.visible?
  end

  attribute :is_calculated do |object|
    object.calculated_field?
  end

  attribute :is_lookup do |object|
    object.field_type == 'lookup' || object.lookup_source_type.present?
  end

  attribute :has_options do |object|
    object.form_field_options.any?
  end

  attribute :options_count do |object|
    object.form_field_options.count
  end

  attribute :validations_count do |object|
    object.form_field_validations.active.count
  end

  attribute :has_dependencies do |object|
    object.depends_on.present? || object.parent_field_id.present?
  end

  # Form section relationship
  attribute :form_section_id do |object|
    object.form_section_id
  end

  attribute :form_section_name do |object|
    object.form_section&.name
  end

  # Form template relationship (through section)
  attribute :form_template_id do |object|
    object.form_template&.id
  end

  attribute :form_template_name do |object|
    object.form_template&.name
  end

  # Field type definition relationship
  attribute :field_type_definition_name do |object|
    object.field_type_definition&.name
  end

  attribute :field_type_definition_input_type do |object|
    object.field_type_definition&.input_type
  end

  # Relationships
  belongs_to :form_section
  has_many :form_field_options
  has_many :form_field_validations
  belongs_to :parent_field, record_type: :form_field, serializer: :form_field, optional: true
  has_many :dependent_fields, record_type: :form_field, serializer: :form_field
  # Note: field_type_definition relationship excluded due to custom foreign key mapping
end
