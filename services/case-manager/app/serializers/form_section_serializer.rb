class FormSectionSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :name, :title, :description, :display_order, :is_required, :visible,
             :display_condition_field_id, :display_condition_value,
             :display_condition_user_role, :display_condition_project_type,
             :section_type, :sub_form_template_name, :sub_form_button_label, :max_sub_forms,
             :created_at, :updated_at

  # Computed attributes
  attribute :fields_count do |object|
    object.form_fields.count
  end

  attribute :required_fields_count do |object|
    object.form_fields.where(required: true).count
  end

  attribute :has_conditional_logic do |object|
    object.display_condition_field_id.present? ||
    object.display_condition_value.present? ||
    object.display_condition_user_role.present? ||
    object.display_condition_project_type.present?
  end

  attribute :is_visible do |object|
    object.visible?
  end

  # Form template relationship
  attribute :form_template_id do |object|
    object.form_template_id
  end

  attribute :form_template_name do |object|
    object.form_template&.name
  end

  belongs_to :form_template
  has_many :form_fields
end
