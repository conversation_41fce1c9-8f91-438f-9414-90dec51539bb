class FormSubmissionSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :case_id, :form_section_id, :form_data, :status,
             :submitted_at, :completed_at, :created_by_id, :updated_by_id,
             :created_at, :updated_at

  # Computed attributes
  attribute :completion_percentage do |object|
    object.completion_percentage
  end

  attribute :can_be_submitted do |object|
    object.can_be_submitted?
  end

  attribute :is_draft do |object|
    object.form_draft?
  end

  attribute :is_in_progress do |object|
    object.form_in_progress?
  end

  attribute :is_submitted do |object|
    object.form_submitted?
  end

  attribute :is_completed do |object|
    object.form_completed?
  end

  attribute :validation_errors do |object|
    object.validate_form_data
  end

  attribute :section_completion_status do |object|
    object.section_completion_status
  end

  # Computed template information (accessed through section)
  attribute :form_template_id do |object|
    object.form_template.id
  end

  attribute :template_name do |object|
    object.template_name
  end

  attribute :section_name do |object|
    object.section_name
  end

  # Relationships
  belongs_to :case, serializer: CaseSerializer
  has_one :form_template, through: :form_section, serializer: FormTemplateSerializer
  belongs_to :form_section, serializer: FormSectionSerializer

  # User relationships (using RemoteUserSerializer for GRPC users)
  belongs_to :created_by, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :created_by_id
  belongs_to :updated_by, record_type: :remote_user, serializer: RemoteUserSerializer, id_method_name: :updated_by_id, optional: true
end
