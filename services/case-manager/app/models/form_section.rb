class FormSection < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  belongs_to :form_template
  has_many :form_fields, dependent: :destroy
  has_many :form_submissions, dependent: :destroy
  belongs_to :display_condition_field, class_name: 'FormField', optional: true

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :title, presence: true, length: { minimum: 2, maximum: 200 }
  validates :display_order, presence: true, numericality: { greater_than: 0 }
  validates :display_order, uniqueness: { scope: :form_template_id }
  validates :section_type, presence: true

  # Validation rules based on section type
  validates :form_fields, presence: true, if: :form?
  validates :sub_form_template_name, presence: true, if: :list?
  validates :sub_form_button_label, presence: true, if: :list?

  # Enums
  enum :section_type, {
    form: 'form',    # Traditional form fields rendered in page
    list: 'list'     # Collection of items managed via modal dialogs
  }

  # Scopes
  scope :visible, -> { where(visible: true) }
  scope :required, -> { where(is_required: true) }
  scope :ordered, -> { order(:display_order) }
  scope :form_sections, -> { where(section_type: 'form') }
  scope :list_sections, -> { where(section_type: 'list') }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name title display_order is_required visible form_template_id created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_template form_fields display_condition_field]
  end

  # Instance methods
  def should_display?(form_data = {}, user = nil)
    return false unless visible?

    # Check user role condition
    if display_condition_user_role.present?
      return false unless user&.role&.name&.include?(display_condition_user_role)
    end

    # Check project type condition
    if display_condition_project_type.present?
      return false unless user&.project&.project_type == display_condition_project_type
    end

    # Check field value condition
    if display_condition_field.present? && display_condition_value.present?
      field_value = form_data[display_condition_field.field_name]
      return field_value.to_s == display_condition_value.to_s
    end

    true
  end

  def completion_percentage(form_data = {})
    return 0 if form_fields.empty?

    completed_fields = form_fields.count do |field|
      field.is_completed?(form_data)
    end

    (completed_fields.to_f / form_fields.count * 100).round(2)
  end

  def required_fields_completed?(form_data = {})
    required_fields = form_fields.where(required: true)
    return true if required_fields.empty?

    required_fields.all? { |field| field.is_completed?(form_data) }
  end

  def section_structure
    {
      id: id,
      name: name,
      title: title,
      description: description,
      display_order: display_order,
      is_required: is_required,
      visible: visible,
      display_conditions: {
        field_id: display_condition_field_id,
        value: display_condition_value,
        user_role: display_condition_user_role,
        project_type: display_condition_project_type
      },
      fields: form_fields.ordered.map(&:field_structure)
    }
  end

  def duplicate_for_template(new_template)
    new_section = self.dup
    new_section.form_template = new_template

    if new_section.save
      form_fields.each do |field|
        field.duplicate_for_section(new_section)
      end
    end

    new_section
  end



  def total_fields_count
    form_fields.count
  end

  def required_fields_count
    form_fields.where(required: true).count
  end

  # List-type section methods
  def sub_form_template
    FormTemplate.find_by(name: sub_form_template_name) if list?
  end

  def submissions_for_case(case_obj)
    form_submissions.where(case: case_obj).order(:submission_order)
  end

  def can_add_more_submissions?(case_obj)
    return true unless max_sub_forms.present?

    current_count = submissions_for_case(case_obj).count
    current_count < max_sub_forms
  end

  def sub_form_button_text
    sub_form_button_label.presence || "Add #{title}"
  end

  # Enhanced section structure for list sections
  def section_structure_with_submissions(case_obj = nil)
    base_structure = section_structure

    if list? && case_obj
      base_structure.merge({
        section_type: 'list',
        sub_form_template_name: sub_form_template_name,
        sub_form_button_label: sub_form_button_text,
        max_sub_forms: max_sub_forms,
        submissions: submissions_for_case(case_obj).map do |submission|
          {
            id: submission.id,
            form_data: submission.form_data,
            submission_order: submission.submission_order,
            status: submission.status,
            created_at: submission.created_at,
            updated_at: submission.updated_at
          }
        end,
        can_add_more: can_add_more_submissions?(case_obj)
      })
    else
      base_structure.merge({ section_type: section_type })
    end
  end
end
