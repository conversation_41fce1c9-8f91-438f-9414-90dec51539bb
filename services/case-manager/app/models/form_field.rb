class FormField < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Associations
  belongs_to :form_section
  has_one :form_template, through: :form_section
  has_many :form_field_options, dependent: :destroy
  has_many :form_field_validations, dependent: :destroy
  has_many :dependent_fields, class_name: "<PERSON><PERSON><PERSON>", foreign_key: "parent_field_id", dependent: :nullify
  belongs_to :parent_field, class_name: "Form<PERSON>ield", optional: true
  belongs_to :field_type_definition, foreign_key: "field_type", primary_key: "name", optional: true

  # Validations
  validates :field_name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :label, presence: true, length: { minimum: 2, maximum: 200 }
  validates :field_type, presence: true
  validates :data_type, presence: true
  validates :display_order, presence: true, numericality: { greater_than: 0 }
  validates :display_order, uniqueness: { scope: :form_section_id }

  # For model-based lookups - store the model class name
  validates :lookup_source_type, inclusion: {
    in: %w[StaffPosition PartnerAgency GeographicLocation CaseType ServiceCategory ReferralSource static case_data beneficiary_data]
  }, allow_blank: true

  # Field configuration (JSON) - stores field-specific settings
  validates :field_config, format: { with: /\A\{.*\}\z/, allow_blank: true }

  # Calculation support
  validates :calculation_formula, presence: true, if: -> { calculated_field? }

  # Field dependencies for calculations and conditional logic
  # Store as JSON array: ["field_name_1", "field_name_2"]
  validates :depends_on, format: { with: /\A\[.*\]\z/, allow_blank: true }

  # Scopes
  scope :visible, -> { where(visible: true) }
  scope :required, -> { where(required: true) }
  scope :ordered, -> { order(:display_order) }
  scope :by_field_type, ->(type) { where(field_type: type) if type.present? }
  scope :calculated, -> { where(field_type: "calculated") }
  scope :lookup, -> { where(field_type: "lookup") }

  # Enums
  enum :field_type, {
    text: "text",
    textarea: "textarea",
    number: "number",
    email: "email",
    phone: "phone",
    date: "date",
    datetime: "datetime",
    select_field: "select_field",
    radio: "radio",
    checkbox: "checkbox",
    file: "file",
    calculated: "calculated",
    lookup: "lookup"
  }

  enum :data_type, {
    string_data: "string_data",
    integer_data: "integer_data",
    float_data: "float_data",
    boolean_data: "boolean_data",
    date_data: "date_data",
    datetime_data: "datetime_data",
    json_data: "json_data"
  }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[field_name label field_type data_type display_order required visible created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_section form_template form_field_options form_field_validations parent_field dependent_fields]
  end

  # Calculation and dependency methods
  def calculated_field?
    field_type == "calculated" ||
    (field_type_definition&.input_type == "calculated") ||
    calculation_formula.present?
  end

  def lookup_field?
    field_type == "lookup" || lookup_source_type.present?
  end

  def calculation_dependencies
    return [] unless depends_on.present?

    begin
      JSON.parse(depends_on)
    rescue JSON::ParserError
      []
    end
  end

  def should_calculate_for?(form_data)
    return false unless calculated_field?
    return true if calculation_dependencies.empty?

    calculation_dependencies.any? { |dep| form_data[dep].present? }
  end

  def field_type_config
    return {} unless field_type_definition

    field_type_definition.render_config(self)
  end

  # Instance methods
  def is_completed?(form_data = {})
    field_value = form_data[field_name]

    return true unless required?
    return false if field_value.blank?

    case field_type
    when "checkbox"
      field_value.is_a?(Array) && field_value.any?
    when "file"
      field_value.present? && field_value != "null"
    else
      field_value.present?
    end
  end

  def calculate_value(form_data = {}, case_obj = nil)
    return nil unless calculated?
    return nil if calculation_formula.blank?

    # Simple calculation engine - can be enhanced
    formula = calculation_formula.dup

    # Replace field references with actual values
    form_data.each do |field_name, value|
      formula.gsub!("{{#{field_name}}}", value.to_s)
    end

    # Replace case references
    if case_obj
      formula.gsub!("{{case.age}}", case_obj.beneficiary&.age.to_s)
      formula.gsub!("{{case.duration}}", case_obj.age_in_days.to_s)
    end

    begin
      # Safe evaluation - only allow basic math operations
      if formula.match?(/\A[\d\s\+\-\*\/\(\)\.]+\z/)
        eval(formula)
      else
        nil
      end
    rescue
      nil
    end
  end

  def lookup_value(form_data = {}, case_obj = nil)
    return nil unless lookup?
    return nil if lookup_source_type.blank?

    case lookup_source_type
    when "static"
      config = lookup_source_config.is_a?(String) ? JSON.parse(lookup_source_config) : lookup_source_config
      config["values"]&.sample
    when "case_data"
      config = lookup_source_config.is_a?(String) ? JSON.parse(lookup_source_config) : lookup_source_config
      case_obj&.send(config["field"])
    when "beneficiary_data"
      config = lookup_source_config.is_a?(String) ? JSON.parse(lookup_source_config) : lookup_source_config
      case_obj&.beneficiary&.send(config["field"])
    when "StaffPosition", "PartnerAgency", "GeographicLocation", "CaseType", "ServiceCategory", "ReferralSource"
      # Return lookup options for model-based lookups
      lookup_options(case_obj&.project_id)&.sample
    else
      nil
    end
  rescue
    nil
  end

  # Get lookup options for model-based lookups
  def lookup_options(search: nil, context: {}, limit: 50)
    return [] unless lookup_source_type.present?

    # Handle static lookups
    if lookup_source_type == "static"
      config = lookup_source_config.is_a?(String) ? JSON.parse(lookup_source_config) : lookup_source_config
      options = config["values"] || []

      # Apply search filter
      if search.present?
        options = options.select { |option| option.to_s.downcase.include?(search.downcase) }
      end

      return options.first(limit)
    end

    # Handle model-based lookups
    return [] unless %w[StaffPosition PartnerAgency GeographicLocation CaseType ServiceCategory ReferralSource].include?(lookup_source_type)

    project_id = context[:project_id] || context[:current_project]&.id || context[:user]&.project_id

    base_scope = case lookup_source_type
    when "StaffPosition"
      StaffPosition.active.for_project(project_id)
    when "PartnerAgency"
      PartnerAgency.active.for_project(project_id)
    when "GeographicLocation"
      scope = GeographicLocation.active.for_project(project_id)
      if context[:parent_location_id]
        scope.children_of(context[:parent_location_id])
      else
        scope.countries
      end
    when "CaseType"
      CaseType.active.for_project(project_id)
    when "ServiceCategory"
      ServiceCategory.active.for_project(project_id)
    when "ReferralSource"
      ReferralSource.active.for_project(project_id)
    else
      return []
    end

    # Apply search filter if supported
    if search.present? && base_scope.respond_to?(:search)
      base_scope = base_scope.search(search)
    end

    # Apply limit and return
    base_scope.ordered.limit(limit)
  end

  # Get formatted lookup options for API responses
  def formatted_lookup_options(project_id = nil, context = {})
    lookup_options(project_id, context).map(&:api_representation)
  end

  def field_structure(context = {})
    structure = {
      id: id,
      field_name: field_name,
      label: label,
      field_type: field_type,
      data_type: data_type,
      display_order: display_order,
      required: required,
      visible: visible,
      help_text: help_text,
      placeholder: placeholder,
      validation_rules: validation_rules,
      field_config: field_config,
      calculation_formula: calculation_formula,
      lookup_source_type: lookup_source_type,
      lookup_source_config: lookup_source_config,
      parent_field_id: parent_field_id,
      options: form_field_options.ordered.map(&:option_structure),
      validations: form_field_validations.active.ordered.map(&:validation_structure)
    }

    # Add context-aware computed states
    if context.present?
      structure[:is_enabled] = field_enabled?(context)
      structure[:is_visible] = field_visible?(context)
    end

    # Add lookup data if applicable
    if lookup? && lookup_source_type.present?
      if %w[StaffPosition PartnerAgency GeographicLocation CaseType ServiceCategory ReferralSource].include?(lookup_source_type)
        # Use model-based lookup
        structure[:lookup_options] = formatted_lookup_options(context[:project_id], context)
      end
    end

    structure
  end

  # Context-aware field state methods
  def field_enabled?(context = {})
    # Base visibility check
    return false unless visible?

    # Check parent field dependency
    if parent_field_id.present?
      parent_field = FormField.find_by(id: parent_field_id)
      return false unless parent_field&.field_visible?(context)

      # Check if parent field has a value that enables this field
      form_data = context[:form_submission]&.form_data || context[:form_data] || {}
      parent_value = form_data[parent_field.field_name]
      return false if parent_value.blank?
    end

    # Check field dependencies from depends_on
    if depends_on.present?
      dependencies = calculation_dependencies
      form_data = context[:form_submission]&.form_data || context[:form_data] || {}

      # Field is enabled if at least one dependency has a value
      return dependencies.any? { |dep| form_data[dep].present? }
    end

    # Check user role conditions
    if context[:user_role].present?
      # Add role-based field enabling logic here
      # For now, return true - to be enhanced based on field configuration
    end

    true
  end

  def field_visible?(context = {})
    # Base visibility check
    return false unless visible?

    # Check section-level visibility
    if form_section.present?
      form_data = context[:form_submission]&.form_data || context[:form_data] || {}
      user = context[:user]
      return false unless form_section.should_display?(form_data, user)
    end

    # Check field-level conditional logic
    if field_config.present? && field_config['conditional_logic'].present?
      return evaluate_conditional_logic(field_config['conditional_logic'], context)
    end

    # Check parent field visibility
    if parent_field_id.present?
      parent_field = FormField.find_by(id: parent_field_id)
      return false unless parent_field&.field_visible?(context)
    end

    true
  end

  private

  def evaluate_conditional_logic(logic, context)
    # Simple conditional logic evaluation
    # Format: { "field": "field_name", "operator": "equals", "value": "expected_value" }
    return true unless logic.is_a?(Hash)

    form_data = context[:form_submission]&.form_data || context[:form_data] || {}
    field_value = form_data[logic['field']]
    expected_value = logic['value']

    case logic['operator']
    when 'equals'
      field_value.to_s == expected_value.to_s
    when 'not_equals'
      field_value.to_s != expected_value.to_s
    when 'contains'
      field_value.to_s.include?(expected_value.to_s)
    when 'not_empty'
      field_value.present?
    when 'empty'
      field_value.blank?
    else
      true
    end
  end

  def duplicate_for_section(new_section)
    new_field = self.dup
    new_field.form_section = new_section

    if new_field.save
      form_field_options.each { |option| option.duplicate_for_field(new_field) }
      form_field_validations.each { |validation| validation.duplicate_for_field(new_field) }
    end

    new_field
  end
end
