class FormTemplate < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Associations
  has_many :form_sections, dependent: :destroy
  has_many :form_fields, through: :form_sections
  has_many :form_submissions, dependent: :destroy
  has_many :workflow_progresses, dependent: :destroy

  # Prerequisites relationships
  has_many :form_template_prerequisites, dependent: :destroy
  has_many :prerequisite_templates, through: :form_template_prerequisites
  has_many :dependent_template_prerequisites, class_name: 'FormTemplatePrerequisite',
           foreign_key: 'prerequisite_template_id', dependent: :destroy
  has_many :dependent_templates, through: :dependent_template_prerequisites, source: :form_template

  # Validations
  validates :name, presence: true, uniqueness: true, length: { minimum: 2, maximum: 100 }
  validates :title, presence: true, length: { minimum: 2, maximum: 200 }
  validates :sequence_order, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :project_id, presence: true
  validates :template_type, presence: true

  # Template type specific validations
  validates :sequence_order, presence: true, if: :workflow?

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }

  scope :by_target_role, ->(role) { where(target_role: [ role, "both" ]) if role.present? }
  scope :ordered, -> { order(:sequence_order, :name) }
  scope :for_project, ->(project_id) { where(project_id: project_id) if project_id.present? }
  scope :workflow_templates, -> { where(template_type: 'workflow').order(:sequence_order) }
  scope :component_templates, -> { where(template_type: 'component') }

  # Enums
  enum :template_type, {
    workflow: 'workflow',     # Main case workflow templates (consent, assessment, closure)
    component: 'component'    # Modal dialogs, standalone forms, surveys, etc.
  }

  enum :target_role, {
    case_manager: "case_manager",
    supervisor: "supervisor",
    both: "both"
  }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name title target_role sequence_order active project_id created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_sections form_fields form_submissions]
  end

  # Class methods
  # Define form sequence for progressive completion (based on actual forms)
  def self.form_sequence
    {
      'consent_assent' => [],
      'registration_rapid_assessment' => [ 'consent_assent' ],
      'comprehensive_assessment' => [ 'consent_assent', 'registration_rapid_assessment' ],
      'service_planning' => [ 'consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment' ]
    }
  end

  def self.setup_prerequisites!
    form_sequence.each do |form_name, prerequisites|
      template = find_by(name: form_name)
      next unless template

      template.update!(prerequisite_forms: prerequisites)
    end
  end

  # Instance methods
  def can_be_accessed_by?(user)
    return false unless active?
    return true if target_role == "both"

    case target_role
    when "case_manager"
      user.case_manager?
    when "supervisor"
      user.supervisor?
    else
      false
    end
  end

  def prerequisites_met?(case_obj)
    # Use new prerequisite system if available, fallback to legacy
    if form_template_prerequisites.any?
      prerequisite_templates.all? do |prereq_template|
        submission = case_obj.form_submissions.find_by(form_template: prereq_template)
        submission&.form_completed?
      end
    else
      # Legacy prerequisite system
      return true if prerequisite_forms.blank?

      prerequisite_forms.all? do |prereq_name|
        prereq_template = FormTemplate.find_by(name: prereq_name, project_id: project_id)
        next false unless prereq_template

        submission = case_obj.form_submissions.find_by(form_template: prereq_template)
        submission&.form_completed?
      end
    end
  end

  def total_fields_count
    form_fields.count
  end

  def required_fields_count
    form_fields.where(required: true).count
  end

  def form_structure
    {
      id: id,
      name: name,
      title: title,
      description: description,
      target_role: target_role,
      sequence_order: sequence_order,
      sections: form_sections.includes(:form_fields).ordered.map(&:section_structure)
    }
  end

  def duplicate_for_project(new_project_id, new_name = nil)
    new_template = self.dup
    new_template.name = new_name || "#{name}_copy_#{Time.current.to_i}"
    new_template.project_id = new_project_id
    new_template.active = false

    if new_template.save
      form_sections.each do |section|
        section.duplicate_for_template(new_template)
      end
    end

    new_template
  end

  # Template completion for a case
  def completed_for_case?(case_obj)
    return false if form_sections.empty?

    form_sections.all? { |section| section.completed_for_case?(case_obj) }
  end

  def completion_percentage_for_case(case_obj)
    return 0 if form_sections.empty?

    completed_sections = form_sections.count { |section| section.completed_for_case?(case_obj) }
    (completed_sections.to_f / form_sections.count * 100).round(2)
  end

  # Enhanced form structure with template type
  def form_structure_with_type
    form_structure.merge({
      template_type: template_type,
      is_workflow: workflow?,
      is_component: component?
    })
  end
end
