class FormTemplatePrerequisite < ApplicationRecord
  # Associations
  belongs_to :form_template
  belongs_to :prerequisite_template, class_name: 'FormTemplate'

  # Validations
  validates :form_template_id, uniqueness: { scope: :prerequisite_template_id }
  validate :prevent_circular_dependencies

  # Scopes
  scope :for_template, ->(template_id) { where(form_template_id: template_id) }

  # Class methods
  def self.setup_default_prerequisites!
    # Migrate from hardcoded prerequisites to database relationships
    FormTemplate.find_each do |template|
      next if template.prerequisite_forms.blank?

      template.prerequisite_forms.each do |prereq_name|
        prereq_template = FormTemplate.find_by(name: prereq_name, project_id: template.project_id)
        next unless prereq_template

        # Create prerequisite relationship if it doesn't exist
        find_or_create_by(
          form_template: template,
          prerequisite_template: prereq_template
        )
      end
    end
  end

  def self.prerequisites_for_template(template)
    where(form_template: template)
      .includes(:prerequisite_template)
      .map(&:prerequisite_template)
  end

  def self.dependents_of_template(template)
    where(prerequisite_template: template)
      .includes(:form_template)
      .map(&:form_template)
  end

  # Instance methods
  def prerequisite_met_for_case?(case_obj)
    submission = case_obj.form_submissions.find_by(form_template: prerequisite_template)
    submission&.form_completed?
  end

  private

  def prevent_circular_dependencies
    return unless form_template_id && prerequisite_template_id

    # Check if this would create a circular dependency
    if would_create_circular_dependency?
      errors.add(:prerequisite_template, 'would create a circular dependency')
    end
  end

  def would_create_circular_dependency?
    # Check if prerequisite_template has form_template as a prerequisite (direct or indirect)
    visited = Set.new
    check_circular_dependency(prerequisite_template_id, form_template_id, visited)
  end

  def check_circular_dependency(current_template_id, target_template_id, visited)
    return false if visited.include?(current_template_id)
    return true if current_template_id == target_template_id

    visited.add(current_template_id)

    # Get all prerequisites of current template
    prerequisites = FormTemplatePrerequisite.where(form_template_id: current_template_id)
                                           .pluck(:prerequisite_template_id)

    prerequisites.any? do |prereq_id|
      check_circular_dependency(prereq_id, target_template_id, visited)
    end
  end
end
