# frozen_string_literal: true

# TODO: Temporary built this way, the data loading shuold be handled in ActiveRpc not here

# RemoteUser represents a User from the Core service as an ActiveStruct instance
# This model provides a clean interface for working with User data in the Case Manager service
class RemoteUser < AtharAuth::Models::User
  # Override attribute accessors to fetch data from Core service via GRPC

  def name
    @name ||= fetch_user_data&.name || "Unknown User"
  end

  def email
    @email ||= fetch_user_data&.email || "<EMAIL>"
  end

  def status
    @status ||= fetch_user_data&.status || "active"
  end

  def avatar_url
    @avatar_url ||= fetch_user_data&.avatar_attributes&.url
  end

  def avatar_attributes
    @avatar_attributes ||= begin
      data = fetch_user_data&.avatar_attributes
      if data.present?
        RemoteUsers::AvatarStruct.new(data.to_hash)
      else
        RemoteUsers::AvatarStruct.new
      end
    end
  end

  # Class method to create a RemoteUser and fetch data
  def self.find(user_id)
    user = new(id: user_id)
    user.fetch_user_data! # Force fetch on creation
    user
  end

  # Force fetch user data from Core service
  def fetch_user_data!
    @user_data = fetch_user_data
    if @user_data
      @name = @user_data.name
      @email = @user_data.email
      @status = @user_data.status
      @avatar_attributes = @user_data.avatar_attributes if @user_data.respond_to?(:avatar_attributes)
      @avatar_url = @avatar_attributes&.url if @avatar_attributes
    end
    @user_data
  end

  private

  # Fetch user data from Core service via GRPC
  def fetch_user_data
    return @user_data if @user_data
    return nil unless id.present?

    begin
      # Use the GRPC client factory to get the correct client
      client = AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")
      response = client.call(:GetUser, id: id.to_i)
      @user_data = response.message
    rescue => e
      Rails.logger.warn "Failed to fetch user data for ID #{id}: #{e.message}" if defined?(Rails)
      puts "GRPC Error: #{e.message}"
      nil
    end
  end
end
