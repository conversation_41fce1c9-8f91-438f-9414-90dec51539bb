class FormSubmission < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Associations
  belongs_to :case, class_name: 'Case', foreign_key: 'case_id'
  belongs_to :form_section
  has_one :form_template, through: :form_section

  # Validations
  validates :case_id, presence: true
  validates :created_by_id, presence: true
  validates :case_id, uniqueness: { scope: :form_section_id }

  # Scopes (enum automatically creates scopes for status values)
  scope :recent, -> { order(updated_at: :desc) }
  scope :by_status, ->(status) { where(status: status) if status.present? }
  scope :ordered, -> { order(:submission_order, :created_at) }
  scope :for_section, ->(section_id) { where(form_section_id: section_id) if section_id.present? }
  scope :for_template, ->(template) { joins(:form_section).where(form_sections: { form_template: template }) }
  scope :workflow_submissions, -> { joins(:form_template).where(form_templates: { template_type: "workflow" }) }
  scope :component_submissions, -> { joins(:form_template).where(form_templates: { template_type: "component" }) }

  # Enums
  enum :status, {
    form_draft: 0,
    form_in_progress: 1,
    form_submitted: 2,
    form_completed: 3
  }

  # Callbacks
  after_save :update_case_beneficiary_data, if: :registration_form?
  after_create :submit_for_completion_if_ready
  after_update :update_completion_status!, if: :saved_change_to_form_data?

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[case_id created_by_id updated_by_id status submitted_at completed_at created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_template form_section case]
  end

  # Instance methods
  def completion_percentage
    return 0 if form_template.form_fields.empty?

    completed_fields = form_template.form_fields.count do |field|
      field.is_completed?(form_data)
    end

    (completed_fields.to_f / form_template.form_fields.count * 100).round(2)
  end

  def section_completion_status
    form_template.form_sections.map do |section|
      {
        section_id: section.id,
        section_name: section.name,
        completion_percentage: section.completion_percentage(form_data),
        required_fields_completed: section.required_fields_completed?(form_data)
      }
    end
  end

  def can_be_submitted?
    return false unless form_in_progress? || form_draft?

    # Check if all required fields are completed
    form_template.form_fields.where(required: true).all? do |field|
      field.is_completed?(form_data)
    end
  end

  def submit!
    return false unless can_be_submitted?

    update!(
      status: :form_submitted,
      submitted_at: Time.current
    )
  end

  def complete!
    return false unless form_submitted?

    update!(
      status: :form_completed,
      completed_at: Time.current
    )
  end

  def update_field_value(field_name, value, user_id)
    self.form_data ||= {}
    self.form_data[field_name] = value
    self.updated_by_id = user_id

    # Auto-calculate status based on completion
    if form_draft? && form_data.any?
      self.status = :form_in_progress
    end

    save!
  end

  def get_field_value(field_name)
    form_data&.dig(field_name)
  end

  def validate_form_data
    errors = []

    form_template.form_fields.each do |field|
      field_value = get_field_value(field.field_name)

      # Check field validations
      field.form_field_validations.active.each do |validation|
        result = validation.validate_value(field_value)
        unless result[:valid]
          errors << {
            field: field.field_name,
            error: result[:error]
          }
        end
      end
    end

    errors
  end

  def form_summary
    {
      id: id,
      form_template: {
        id: form_template.id,
        name: form_template.name,
        title: form_template.title,

      },
      case_id: case_id,
      status: status,
      completion_percentage: completion_percentage,
      submitted_at: submitted_at,
      completed_at: completed_at,
      created_at: created_at,
      updated_at: updated_at
    }
  end

  def duplicate_for_case(new_case_id, user_id)
    new_submission = self.dup
    new_submission.case_id = new_case_id
    new_submission.created_by_id = user_id
    new_submission.updated_by_id = user_id
    new_submission.status = :draft
    new_submission.submitted_at = nil
    new_submission.completed_at = nil
    new_submission.save
    new_submission
  end

  def calculate_all_fields
    calculated_fields = form_template.form_fields.calculated

    calculated_fields.each do |field|
      calculated_value = field.calculate_value(form_data, self.case)
      if calculated_value
        form_data[field.field_name] = calculated_value
      end
    end

    save! if changed?
  end

  def lookup_all_fields
    lookup_fields = form_template.form_fields.lookup

    lookup_fields.each do |field|
      lookup_value = field.lookup_value(form_data, self.case)
      if lookup_value
        form_data[field.field_name] = lookup_value
      end
    end

    save! if changed?
  end

  # Template and section type helper methods (public for serializer access)
  def template_name
    form_section.form_template.name
  end

  def section_name
    form_section.name
  end

  private

  def registration_form?
    form_template.name == 'registration_rapid_assessment'
  end

  def update_case_beneficiary_data
    self.case.update_beneficiary_data!
  end

  def submit_for_completion_if_ready
    # Check if form is complete and update case accordingly
    if form_completed?
      update!(status: 'completed')
      self.case.update_beneficiary_data! if registration_form?
      self.case.check_approval_readiness! # Check if case can be submitted for approval
    end
  end

  def update_completion_status!
    # Update completion status when form data changes
    if completion_percentage >= 100.0 && !form_completed?
      update!(status: :form_completed, completed_at: Time.current)
    elsif completion_percentage < 100.0 && form_completed?
      update!(status: :form_in_progress, completed_at: nil)
    end
  end

  def is_workflow_submission?
    form_template.template_type == "workflow"
  end

  def is_component_submission?
    form_template.template_type == "component"
  end

  # Create a new submission for a section
  def self.create_for_section(case_obj, form_section, user_id, initial_data = {})
    # Calculate next submission order for this section
    next_order = FormSubmission.where(
      case: case_obj,
      form_section: form_section
    ).maximum(:submission_order).to_i + 1

    # Create submission (template accessed via section)
    FormSubmission.create!(
      case: case_obj,
      form_section: form_section,
      submission_order: next_order,
      created_by_id: user_id,
      status: :form_draft,
      form_data: initial_data
    )
  end
end
