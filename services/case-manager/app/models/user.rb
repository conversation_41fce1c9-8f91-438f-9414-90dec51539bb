# frozen_string_literal: true

class User < AtharAuth::Models::User
  # Inherit all common attributes from AtharAuth::Models::User:
  # - id, name, email, global, project_id, user_type, scope, permissions
  # - global_user?, project_based_user?, can?(permission)
  # - from_token_data factory method with rich associations

  # CM-specific associations
  has_many :managed_cases, class_name: "Case", foreign_key: :assigned_user_id, primary_key: :id
  has_many :created_cases, class_name: "Case", foreign_key: :created_by_id, primary_key: :id
  has_many :case_comments, class_name: "Comment", foreign_key: :user_id, primary_key: :id
  has_many :uploaded_documents, class_name: "CaseDocument", foreign_key: :uploaded_by_id, primary_key: :id

  # Enhanced role checking methods
  def case_manager?
    role&.name&.include?("case_manager")
  end

  def supervisor?
    role&.name&.include?("supervisor")
  end

  # CM-specific domain methods
  def accessible_cases
    if global_user?
      Case.all
    elsif supervisor?
      Case.where(project_id: project_id)
    elsif case_manager?
      managed_cases.where(project_id: project_id)
    else
      Case.none
    end
  end

  def can_manage_case?(case_obj)
    return true if global_user?
    return true if supervisor? && case_obj.project_id == project_id
    return true if case_manager? && case_obj.assigned_user_id == id && case_obj.project_id == project_id

    false
  end
end
