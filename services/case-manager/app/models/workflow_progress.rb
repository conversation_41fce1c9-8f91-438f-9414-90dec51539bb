class WorkflowProgress < ApplicationRecord
  # Associations
  belongs_to :form_template
  # Note: case_id is UUID, no direct belongs_to due to cross-service architecture

  # Validations
  validates :case_id, presence: true
  validates :completion_percentage, presence: true,
            numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }
  validates :sections_completed, presence: true,
            numericality: { greater_than_or_equal_to: 0 }
  validates :total_sections, presence: true,
            numericality: { greater_than_or_equal_to: 0 }
  validates :case_id, uniqueness: { scope: :form_template_id }

  # Scopes
  scope :for_case, ->(case_id) { where(case_id: case_id) }
  scope :completed, -> { where(completion_percentage: 100.0) }
  scope :in_progress, -> { where('completion_percentage > 0 AND completion_percentage < 100') }
  scope :not_started, -> { where(completion_percentage: 0.0) }
  scope :recent, -> { order(last_updated_at: :desc) }

  # Class methods
  def self.calculate_for_case_and_template(case_id, form_template)
    progress = find_or_initialize_by(case_id: case_id, form_template: form_template)
    progress.recalculate!
    progress
  end

  def self.refresh_all_for_case(case_id)
    # Get all templates for the case's project
    case_obj = Case.find(case_id) rescue nil
    return unless case_obj

    form_templates = FormTemplate.workflow_templates.for_project(case_obj.project_id)

    form_templates.find_each do |template|
      calculate_for_case_and_template(case_id, template)
    end
  end

  # Instance methods
  def recalculate!
    case_obj = Case.find(case_id) rescue nil
    return false unless case_obj

    sections = form_template.form_sections.visible
    self.total_sections = sections.count

    if total_sections == 0
      self.completion_percentage = 0.0
      self.sections_completed = 0
      self.section_progress = {}
    else
      completed_count = 0
      section_data = {}

      sections.each do |section|
        section_completion = calculate_section_completion(section, case_obj)
        section_data[section.id] = section_completion
        completed_count += 1 if section_completion[:completed]
      end

      self.sections_completed = completed_count
      self.completion_percentage = (completed_count.to_f / total_sections * 100).round(2)
      self.section_progress = section_data
    end

    self.last_updated_at = Time.current
    save!
  end

  def completed?
    completion_percentage >= 100.0
  end

  def in_progress?
    completion_percentage > 0 && completion_percentage < 100
  end

  def not_started?
    completion_percentage == 0.0
  end

  def progress_summary
    {
      case_id: case_id,
      form_template: {
        id: form_template.id,
        name: form_template.name,
        title: form_template.title,
        template_type: form_template.template_type
      },
      completion_percentage: completion_percentage,
      sections_completed: sections_completed,
      total_sections: total_sections,
      status: progress_status,
      last_updated_at: last_updated_at,
      section_progress: section_progress
    }
  end

  def progress_status
    return 'completed' if completed?
    return 'in_progress' if in_progress?
    'not_started'
  end

  private

  def calculate_section_completion(section, case_obj)
    case section.section_type
    when 'form'
      calculate_form_section_completion(section, case_obj)
    when 'list'
      calculate_list_section_completion(section, case_obj)
    else
      { completed: false, percentage: 0.0, details: 'Unknown section type' }
    end
  end

  def calculate_form_section_completion(section, case_obj)
    submission = case_obj.form_submissions.find_by(form_template: form_template)

    if submission
      percentage = section.completion_percentage(submission.form_data || {})
      required_completed = section.required_fields_completed?(submission.form_data || {})

      {
        completed: required_completed && percentage >= 100.0,
        percentage: percentage,
        required_fields_completed: required_completed,
        submission_id: submission.id
      }
    else
      {
        completed: false,
        percentage: 0.0,
        required_fields_completed: false,
        submission_id: nil
      }
    end
  end

  def calculate_list_section_completion(section, case_obj)
    submissions = section.submissions_for_case(case_obj)

    # For list sections, completion depends on business rules
    # For now, consider completed if at least one submission exists
    has_submissions = submissions.any?

    {
      completed: has_submissions,
      percentage: has_submissions ? 100.0 : 0.0,
      submissions_count: submissions.count,
      submission_ids: submissions.pluck(:id)
    }
  end
end
