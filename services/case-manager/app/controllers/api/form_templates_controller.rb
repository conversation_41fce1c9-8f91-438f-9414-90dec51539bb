module Api
  class FormTemplatesController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources


    api! "Lists all form templates"
    header "Authorization", "Scoped session token as Bearer token", required: true

    param :target_role, String, desc: "Filter by target role (case_manager, supervisor, both)"
    param :active, [ true, false ], desc: "Filter by active status"
    param_group :pagination_params
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all form templates available for the current project.
      Supports filtering by target role and active status.
      Requires permission: <code>:read, :form_template</code>.
    HTML
    )
    returns code: 200, desc: "List of form templates"

    def index
      apply_filters(@form_templates) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific form template"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form template"
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific form template by ID.
      Includes all form sections and fields for dynamic form rendering.
      Requires permission: <code>:read, :form_template</code>.
    HTML
    )
    returns code: 200, desc: "Form template details with sections and fields"

    def show
      serialize_response(@form_template)
    end





    api! "Validate form data against template"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form template"
    param :form_data, Hash, required: true, desc: "Form data to validate (field_id => value)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Validates form data against the form template's field requirements and validations.
      Returns validation errors and completion status.
      Requires permission: <code>:read, :form_template</code>.
    HTML
    )
    returns code: 200, desc: "Validation results"

    def validate_data
      form_data = params[:form_data] || {}
      validation_result = @form_template.validate_form_data(form_data)
      render json: {
        form_template_id: @form_template.id,
        is_valid: validation_result[:is_valid],
        completion_percentage: validation_result[:completion_percentage],
        validation_errors: validation_result[:errors],
        missing_required_fields: validation_result[:missing_required],
        field_validations: validation_result[:field_validations]
      }
    end

    private

    # Note: Form templates are typically managed through seeds or admin interface
    # Create/Update/Delete endpoints would be added here if needed for dynamic template management
  end
end
