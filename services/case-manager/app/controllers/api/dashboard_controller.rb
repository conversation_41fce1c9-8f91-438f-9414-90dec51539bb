module Api
  class DashboardController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    api! "Get comprehensive dashboard overview"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :date_range, String, desc: "Date range filter: 'this_month', 'last_month', 'this_year', 'custom'"
    param :start_date, String, desc: "Start date for custom range (YYYY-MM-DD)"
    param :end_date, String, desc: "End date for custom range (YYYY-MM-DD)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns comprehensive dashboard overview matching Arabic interface requirements.
      Includes statistics cards, pie charts, recent observations, team data, and analytics.
      Requires permission: <code>:read, :dashboard</code>.

      <b>Date Range Options:</b>
      - this_month: Current month data
      - last_month: Previous month data
      - this_year: Current year data
      - custom: Use start_date and end_date parameters
    HTML
    )
    returns code: 200, desc: "Comprehensive dashboard data"

    def index
      unless current_user.can?(:read, :dashboard)
        return render json: { error: "Access denied" }, status: :forbidden
      end

      service = DashboardStatisticsService.new(current_user, date_range_params)
      dashboard_data = service.dashboard_overview

      render json: {
        data: dashboard_data,
        meta: {
          user_role: current_user.role&.name,
          project_id: current_user.project_id,
          generated_at: Time.current,
          date_range: date_range_params
        }
      }
    end

    # Legacy endpoint for backward compatibility
    def show
      index
    end

    api! "Get case metrics"
    header "Authorization", "Scoped session token as Bearer token", required: true
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns detailed case metrics and statistics.
      Includes case counts by status, type, priority, and trends.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Case metrics data"

    def case_metrics
      unless current_user.can?(:read, :case)
        return render json: { error: "Access denied" }, status: :forbidden
      end

      render json: case_metrics_data
    end

    api! "Get team metrics"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :date_range, String, desc: "Date range filter"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns team performance metrics and workload distribution.
      Shows case manager workloads and team statistics.
      Requires permission: <code>:read, :team_metrics</code>.
    HTML
    )
    returns code: 200, desc: "Team metrics data"

    def team_metrics
      unless current_user.supervisor?
        return render json: { error: "Access denied" }, status: :forbidden
      end

      service = DashboardStatisticsService.new(current_user, date_range_params)

      render json: {
        data: {
          employees: service.employees_statistics,
          team_sharing: service.team_sharing_table_data,
          current_cases_with_others: service.current_cases_with_others_data,
          workload_distribution: service.workload_distribution_analytics
        }
      }
    end

    api! "Get analytics data"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :date_range, String, desc: "Date range filter"
    param :analytics_type, String, desc: "Type of analytics: 'trends', 'forms', 'workload'"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns advanced analytics data including trends, form completion analytics,
      and workload distribution. Provides data for charts and visualizations.
      Requires permission: <code>:read, :analytics</code>.
    HTML
    )
    returns code: 200, desc: "Analytics data"

    def analytics
      unless current_user.can?(:read, :analytics)
        return render json: { error: "Access denied" }, status: :forbidden
      end

      service = DashboardStatisticsService.new(current_user, date_range_params)
      analytics_type = params[:analytics_type] || "all"

      analytics_data = case analytics_type
      when "trends"
                        { trends: service.case_completion_trends }
      when "forms"
                        { forms: service.form_completion_analytics }
      when "workload"
                        { workload: service.workload_distribution_analytics }
      else
                        {
                          trends: service.case_completion_trends,
                          forms: service.form_completion_analytics,
                          workload: service.workload_distribution_analytics
                        }
      end

      render json: { data: analytics_data }
    end

    private

    def user_summary
      {
        id: current_user.id,
        name: current_user.name,
        role: current_user.role&.name,
        project_id: current_project&.id,
        project_name: current_project&.name,
        permissions: current_user.permissions.pluck(:name)
      }
    end

    def case_metrics
      accessible_cases = current_user.accessible_cases

      {
        total_cases: accessible_cases.count,
        cases_by_status: {
          draft: accessible_cases.case_draft.count,
          ready_for_approval: accessible_cases.case_ready_for_approval.count,
          pending_approval: accessible_cases.case_pending_approval.count,
          approved: accessible_cases.case_approved.count,
          active: accessible_cases.case_active.count,
          closed: accessible_cases.case_closed.count,
          suspended: accessible_cases.case_suspended.count
        },
        cases_by_type: accessible_cases.group(:case_type).count,
        cases_by_priority: accessible_cases.group(:priority_level).count,
        urgent_cases: accessible_cases.where(priority_level: [ 4, 5 ]).count,
        overdue_cases: overdue_cases_count(accessible_cases),
        completion_rates: form_completion_rates(accessible_cases)
      }
    end

    def form_metrics
      accessible_cases = current_user.accessible_cases
      form_submissions = FormSubmission.joins(:case).where(cases: { id: accessible_cases.ids })

      {
        total_submissions: form_submissions.count,
        submissions_by_status: form_submissions.group(:status).count,
        submissions_by_template: form_submissions.joins(:form_template)
                                               .group("form_templates.name")
                                               .count,
        average_completion_time: average_form_completion_time(form_submissions),
        forms_needing_attention: forms_needing_attention_count
      }
    end

    def team_metrics_data
      return {} unless current_user.supervisor?

      project_cases = Case.where(project_id: current_project&.id)
      team_members = User.joins(:managed_cases)
                        .where(cases: { project_id: current_project&.id })
                        .distinct

      {
        total_team_members: team_members.count,
        active_assigned_users: team_members.joins(:managed_cases)
                                         .where(cases: { status: [ "active", "approved" ] })
                                         .distinct.count,
        workload_distribution: workload_distribution(team_members),
        team_performance: team_performance_metrics(team_members),
        pending_approvals: project_cases.case_pending_approval.count
      }
    end

    def recent_activity
      accessible_cases = current_user.accessible_cases

      recent_cases = accessible_cases.order(updated_at: :desc).limit(5)
      recent_comments = Comment.joins(:case)
                              .where(cases: { id: accessible_cases.ids })
                              .order(created_at: :desc)
                              .limit(5)
      recent_submissions = FormSubmission.joins(:case)
                                        .where(cases: { id: accessible_cases.ids })
                                        .order(updated_at: :desc)
                                        .limit(5)

      {
        recent_cases: recent_cases.map(&:case_summary),
        recent_comments: recent_comments.map do |comment|
          {
            id: comment.id,
            case_id: comment.case_id,
            content: comment.content.truncate(100),
            user_name: comment.user&.name,
            created_at: comment.created_at
          }
        end,
        recent_submissions: recent_submissions.map do |submission|
          {
            id: submission.id,
            case_id: submission.case_id,
            form_name: submission.form_template&.name,
            status: submission.status,
            updated_at: submission.updated_at
          }
        end
      }
    end

    def pending_tasks
      accessible_cases = current_user.accessible_cases

      tasks = []

      # Cases needing approval
      if current_user.supervisor?
        pending_approvals = accessible_cases.case_pending_approval.count
        tasks << {
          type: "approval",
          count: pending_approvals,
          description: "Cases pending approval",
          priority: "high"
        } if pending_approvals > 0
      end

      # Overdue cases
      overdue_count = overdue_cases_count(accessible_cases)
      tasks << {
        type: "overdue",
        count: overdue_count,
        description: "Overdue cases requiring attention",
        priority: "urgent"
      } if overdue_count > 0

      # Unresolved comments
      unresolved_comments = Comment.joins(:case)
                                  .where(cases: { id: accessible_cases.ids })
                                  .open.count
      tasks << {
        type: "comments",
        count: unresolved_comments,
        description: "Unresolved comments",
        priority: "medium"
      } if unresolved_comments > 0

      tasks
    end

    def case_metrics_data
      case_metrics
    end

    def overdue_cases_count(cases)
      # Cases active for more than 30 days without recent activity
      cases.case_active
           .where("cases.updated_at < ?", 30.days.ago)
           .count
    end

    def form_completion_rates(cases)
      total_cases = cases.count
      return {} if total_cases.zero?

      FormTemplate.where(project_id: current_project&.id).map do |template|
        completed_count = cases.joins(:form_submissions)
                              .where(form_submissions: {
                                form_template: template,
                                status: "form_completed"
                              }).count

        {
          form_name: template.name,
          completion_rate: (completed_count.to_f / total_cases * 100).round(2),
          completed_count: completed_count,
          total_cases: total_cases
        }
      end
    end

    def average_form_completion_time(submissions)
      completed_submissions = submissions.where.not(submitted_at: nil)
      return 0 if completed_submissions.empty?

      total_time = completed_submissions.sum do |submission|
        (submission.submitted_at - submission.created_at) / 1.day
      end

      (total_time / completed_submissions.count).round(2)
    end

    def forms_needing_attention_count
      # Forms that have been in progress for more than 7 days
      FormSubmission.joins(:case)
                   .where(cases: { id: current_user.accessible_cases.ids })
                   .form_in_progress
                   .where("form_submissions.updated_at < ?", 7.days.ago)
                   .count
    end

    def workload_distribution(team_members)
      team_members.map do |member|
        active_cases = member.managed_cases.case_active.count
        pending_cases = member.managed_cases.case_pending_approval.count

        {
          user_id: member.id,
          user_name: member.name,
          active_cases: active_cases,
          pending_cases: pending_cases,
          total_workload: active_cases + pending_cases
        }
      end
    end

    def team_performance_metrics(team_members)
      {
        average_cases_per_manager: team_members.joins(:managed_cases)
                                              .group("users.id")
                                              .count
                                              .values
                                              .sum.to_f / team_members.count,
        cases_closed_this_month: Case.where(assigned_user_id: team_members.ids)
                                    .case_closed
                                    .where(closed_at: 1.month.ago..Time.current)
                                    .count
      }
    end
  end
end
