module Api
  class FieldCalculationsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources
    map_action_permission :calculate, :read
    map_action_permission :calculate_all_fields, :read

    api! "Calculate a specific field value"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form field"
    param :value, String, desc: "Source value for calculation"
    param :form_data, Hash, desc: "Complete form data for context"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Calculates the value for a specific calculated field based on the provided source value
      and form data context. The field must be configured as a calculated field.
      Requires permission: <code>:read, :form_field</code>.
    HTML
    )
    returns code: 200, desc: "Calculated field value"

    def calculate
      field = FormField.find(params[:id])
      return render_not_calculable unless field.calculated_field?

      source_value = calculation_params[:value]
      form_data = calculation_params[:form_data] || {}
      
      calculated_value = FormFieldCalculationService.calculate_field_value(field, form_data)

      render json: {
        field_id: field.id,
        field_name: field.field_name,
        value: calculated_value,
        calculated_at: Time.current.iso8601
      }
    end

    api! "Calculate all calculated fields for a form template"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :form_template_id, Integer, required: true, desc: "ID of the form template"
    param :form_data, Hash, required: true, desc: "Complete form data for calculations"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Calculates values for all calculated fields in a form template based on the provided
      form data. Only fields that have their dependencies satisfied will be calculated.
      Requires permission: <code>:read, :form_template</code>.
    HTML
    )
    returns code: 200, desc: "All calculated field values"

    def calculate_all_fields
      form_template = FormTemplate.find(params[:form_template_id])
      form_data = calculation_params[:form_data] || {}

      calculated_values = FormFieldCalculationService.calculate_all_fields(form_template, form_data)

      render json: {
        form_template_id: form_template.id,
        calculated_values: calculated_values,
        calculated_at: Time.current.iso8601
      }
    end

    api! "Recalculate dependent fields when a field value changes"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :form_template_id, Integer, required: true, desc: "ID of the form template"
    param :changed_field_name, String, required: true, desc: "Name of the field that changed"
    param :form_data, Hash, required: true, desc: "Updated form data"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Recalculates only the fields that depend on the specified changed field.
      This is more efficient than recalculating all fields when only one field changes.
      Requires permission: <code>:read, :form_template</code>.
    HTML
    )
    returns code: 200, desc: "Recalculated dependent field values"

    def recalculate_dependent_fields
      form_template = FormTemplate.find(params[:form_template_id])
      changed_field_name = params[:changed_field_name]
      form_data = calculation_params[:form_data] || {}

      calculated_values = FormFieldCalculationService.recalculate_dependent_fields(
        changed_field_name, 
        form_template, 
        form_data
      )

      render json: {
        form_template_id: form_template.id,
        changed_field: changed_field_name,
        calculated_values: calculated_values,
        calculated_at: Time.current.iso8601
      }
    end

    private

    def calculation_params
      params.permit(:value, :changed_field_name, form_data: {})
    end

    def render_not_calculable
      render json: { error: 'Field is not calculable' }, status: :unprocessable_entity
    end
  end
end
