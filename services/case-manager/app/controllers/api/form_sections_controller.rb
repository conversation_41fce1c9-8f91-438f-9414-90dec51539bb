module Api
  class FormSectionsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources

    api! "Lists form sections with filtering and pagination"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param "filter[form_template_id]", Integer, desc: "Filter by form template"
    param "filter[visible]", String, desc: "Filter by visibility (true/false)"
    description "Returns form sections with commons gem filtering. Requires permission: :read, :form_section"
    returns code: 200, desc: "List of form sections"

    def index
      # @form_sections is set by authorize_resources with project scoping
      apply_filters(@form_sections) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end
  end
end
