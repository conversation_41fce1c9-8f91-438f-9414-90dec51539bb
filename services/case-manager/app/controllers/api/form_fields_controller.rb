module Api
  class FormFieldsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    # Use FormSection permissions since FormFields are part of FormSections
    authorize_resources except: [ :index, :lookup_options, :calculate ]

    # Custom authorization for form field actions using form_section permissions
    before_action :authorize_form_field_access!, only: [ :index, :lookup_options, :calculate ]

    api! "List form fields with optional filtering and context-aware loading"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :form_section_id, Integer, desc: "Filter by form section ID"
    param :case_id, Integer, desc: "Case ID for context-aware field states"
    param :form_submission_id, Integer, desc: "Form submission ID for context"
    param :field_type, String, desc: "Filter by field type"
    param :page, Integer, desc: "Page number for pagination"
    param :per_page, Integer, desc: "Items per page (default: 25)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns form fields with optional filtering and context-aware states.
      When form_section_id is provided, handles both 'form' and 'list' section types.
      For 'list' sections, returns sub-form management data instead of fields.
      For 'form' sections, returns fields with computed states (is_enabled, is_visible).
      Requires permission: <code>:read, :form_field</code>.
    HTML
    )
    returns code: 200, desc: "Paginated form fields or sub-form data"

    def index
      if params[:form_section_id]
        form_section = FormSection.find(params[:form_section_id])

        # Handle list-type sections (item collections managed via modals)
        if form_section.section_type == 'list'
          render_list_section_data(form_section)
        else
          # Form-type section with fields (rendered inline)
          render_form_section_fields(form_section)
        end
      else
        # General field listing
        @form_fields = FormField.all
        @form_fields = @form_fields.where(field_type: params[:field_type]) if params[:field_type]
        @form_fields = @form_fields.page(params[:page]).per(params[:per_page] || 25)

        serialize_response(@form_fields)
      end
    end

    api! "Get lookup options for a specific field"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form field"
    param :search, String, desc: "Search term for filtering options"
    param :case_id, Integer, desc: "Case ID for context-aware lookup filtering"
    param :limit, Integer, desc: "Maximum number of options to return (default: 50)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns lookup options for a specific field. Supports search filtering and
      context-aware option filtering based on case data. Only works with lookup fields.
      Requires permission: <code>:read, :form_field</code>.
    HTML
    )
    returns code: 200, desc: "Field lookup options"

    def lookup_options
      field = FormField.find(params[:id])
      return render_not_lookup_field unless field.lookup_field?

      context = build_context(params)
      options = field.lookup_options(
        search: params[:search],
        context: context,
        limit: params[:limit] || 50
      )

      render json: {
        field_id: field.id,
        field_name: field.field_name,
        lookup_source_type: field.lookup_source_type,
        options: options,
        total_count: options.size,
        searched: params[:search].present?,
        search_term: params[:search]
      }
    end

    # POST /api/form_fields/:id/calculate - Delegate to FieldCalculationsController
    def calculate
      # Delegate to existing FieldCalculationsController logic
      field = FormField.find(params[:id])
      return render_not_calculable unless field.calculated_field?

      source_value = calculation_params[:value]
      form_data = calculation_params[:form_data] || {}

      calculated_value = FormFieldCalculationService.calculate_field_value(field, form_data)

      render json: {
        field_id: field.id,
        field_name: field.field_name,
        value: calculated_value,
        calculated_at: Time.current.iso8601
      }
    end

    private

    def render_list_section_data(form_section)
      context = build_context(params)
      case_obj = context[:case]

      # Get existing sub-submissions for this section
      sub_submissions = case_obj ?
        form_section.submissions_for_case(case_obj) :
        FormSubmission.none

      response_data = {
        section_type: 'list',
        sub_form_template_name: form_section.sub_form_template_name,
        sub_form_button_label: form_section.sub_form_button_label,
        max_sub_forms: form_section.max_sub_forms,
        current_count: sub_submissions.count,
        can_add_more: !form_section.max_sub_forms || sub_submissions.count < form_section.max_sub_forms,
        sub_submissions: sub_submissions.map do |submission|
          {
            id: submission.id,
            form_data: submission.form_data,
            submission_order: submission.submission_order,
            created_at: submission.created_at,
            updated_at: submission.updated_at
          }
        end
      }

      render json: { data: response_data }
    end

    def render_form_section_fields(form_section)
      @form_fields = form_section.form_fields.ordered

      # Apply context-aware conditional logic
      if params[:case_id] || params[:form_submission_id]
        context = build_context(params)
        @form_fields = @form_fields.map do |field|
          field_data = field.field_structure(context)
          # Add computed conditional states
          field_data[:is_enabled] = field_enabled?(field, context)
          field_data[:is_visible] = field_visible?(field, context)
          field_data
        end
      end

      serialize_response(@form_fields)
    end

    def build_context(params)
      context = {}
      context[:case] = Case.find(params[:case_id]) if params[:case_id]
      context[:form_submission] = FormSubmission.find(params[:form_submission_id]) if params[:form_submission_id]
      context[:project_id] = current_project&.id
      context
    end

    def field_enabled?(field, context)
      # Build on existing conditional logic - implement based on field dependencies
      return true # Default implementation - to be enhanced
    end

    def field_visible?(field, context)
      # Build on existing visible attribute and conditional logic
      return field.visible? # Default implementation - to be enhanced
    end

    def serialize_response(fields)
      if fields.respond_to?(:map) && fields.first.is_a?(Hash)
        # Already processed field data
        render json: { data: fields }
      else
        # Use JSONAPI serializer
        render json: FormFieldSerializer.new(fields, serializer_options).serializable_hash
      end
    end

    def serializer_options
      {
        include: [ :form_section, :form_field_options ],
        params: { context: build_context(params) }
      }
    end

    def calculation_params
      params.permit(:value, :changed_field_name, form_data: {})
    end

    def render_not_calculable
      render json: { error: 'Field is not calculable' }, status: :unprocessable_entity
    end

    def render_not_lookup_field
      render json: { error: 'Field is not a lookup field' }, status: :unprocessable_entity
    end

    def authorize_form_field_access!
      # Form fields are accessed through form sections, so check form_section permissions
      unless can?(:read, :form_section)
        render_forbidden("You are not allowed to access form fields")
        return false
      end

      # If accessing fields for a specific section, verify access to that section
      if params[:form_section_id]
        begin
          form_section = FormSection.find(params[:form_section_id])
          # Additional project-based authorization if needed
          unless can?(:read, form_section)
            render_forbidden("You are not allowed to access this form section")
            return false
          end
        rescue ActiveRecord::RecordNotFound
          render json: { error: 'Form section not found' }, status: :not_found
          return false
        end
      end

      true
    end
  end
end
