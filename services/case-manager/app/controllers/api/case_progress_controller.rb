module Api
  class CaseProgressController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources
    map_action_permission :current_template, :read
    map_action_permission :template_progress, :read
    map_action_permission :progress, :read
    map_action_permission :refresh, :update

    api! "Get current template and workflow progress for a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, required: true, desc: "Case ID"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns the current active template and overall workflow progress for a case.
      Includes next steps and prerequisite information.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Current template and workflow progress"

    def current_template
      service = WorkflowProgressService.new(case_id: params[:case_id])
      progress_summary = service.current_progress_summary
      
      render json: {
        case_id: params[:case_id],
        current_progress: progress_summary,
        generated_at: Time.current.iso8601
      }
    end

    api! "Get section progress for specific template"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, required: true, desc: "Case ID"
    param :template_id, Integer, required: true, desc: "Form template ID"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns detailed section-level progress for a specific template and case.
      Includes completion status for each section and field-level details.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Template section progress"

    def template_progress
      service = WorkflowProgressService.new(case_id: params[:case_id])
      template_progress = service.calculate_template_progress(params[:template_id])
      
      render json: {
        case_id: params[:case_id],
        template_id: params[:template_id],
        progress: template_progress,
        generated_at: Time.current.iso8601
      }
    end

    api! "Get comprehensive progress with caching"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, required: true, desc: "Case ID"
    param :refresh, :boolean, desc: "Force refresh of cached progress data"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns comprehensive progress data for all templates associated with a case.
      Uses cached data by default, but can force refresh with refresh=true parameter.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Comprehensive progress data"

    def progress
      service = WorkflowProgressService.new(case_id: params[:case_id])
      
      if params[:refresh] == 'true'
        progress_data = service.calculate_all_progress
      else
        progress_data = service.current_progress_summary
      end
      
      render json: {
        case_id: params[:case_id],
        progress: progress_data,
        cached: params[:refresh] != 'true',
        generated_at: Time.current.iso8601
      }
    end

    api! "Refresh progress calculation for a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, required: true, desc: "Case ID"
    param :template_id, Integer, desc: "Specific template ID to refresh (optional)"
    param :async, :boolean, desc: "Perform refresh asynchronously (default: false)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Triggers a refresh of progress calculation for a case. Can refresh all templates
      or a specific template. Supports both synchronous and asynchronous processing.
      Requires permission: <code>:update, :case</code>.
    HTML
    )
    returns code: 200, desc: "Progress refresh initiated"

    def refresh
      service = WorkflowProgressService.new(case_id: params[:case_id])
      
      if params[:async] == 'true'
        # Asynchronous refresh
        WorkflowProgressService.refresh_case_progress_async(params[:case_id])
        
        render json: {
          case_id: params[:case_id],
          status: 'refresh_initiated',
          async: true,
          message: 'Progress refresh has been queued for background processing',
          initiated_at: Time.current.iso8601
        }
      else
        # Synchronous refresh
        if params[:template_id].present?
          result = service.refresh_progress_for_template(params[:template_id])
          
          render json: {
            case_id: params[:case_id],
            template_id: params[:template_id],
            status: 'refreshed',
            progress: result,
            refreshed_at: Time.current.iso8601
          }
        else
          results = service.calculate_all_progress
          
          render json: {
            case_id: params[:case_id],
            status: 'refreshed',
            progress: results,
            refreshed_at: Time.current.iso8601
          }
        end
      end
    end

    private

    def case_progress_params
      params.permit(:case_id, :template_id, :refresh, :async)
    end
  end
end
