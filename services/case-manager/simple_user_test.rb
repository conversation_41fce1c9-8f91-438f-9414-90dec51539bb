puts "Testing RemoteUser GRPC fetch..."

begin
  user = RemoteUser.find(193)
  puts "User ID: #{user.id}"
  puts "User name: #{user.name}"
  puts "User email: #{user.email}"
  puts "User status: #{user.status}"
rescue => e
  puts "Error: #{e.message}"
  puts "Error class: #{e.class}"
end

puts "Testing with new method..."
begin
  user2 = RemoteUser.new(id: 193)
  puts "User2 ID: #{user2.id}"
  puts "User2 name: #{user2.name}"
rescue => e
  puts "New method error: #{e.message}"
end

puts "Done!"
