output = []
output << "Testing RemoteUser GRPC fetch..."

begin
  user = RemoteUser.new(id: 3)
  output << "User ID: #{user.id}"
  output << "User name: #{user.name}"
  output << "User email: #{user.email}"
  output << "User status: #{user.status}"
rescue => e
  output << "Error: #{e.message}"
  output << "Error class: #{e.class}"
end

output << "Testing with find method..."
begin
  user2 = RemoteUser.find(3)
  output << "Found user: #{user2.name}"
rescue => e
  output << "Find error: #{e.message}"
end

output << "Done!"

# Write to file
File.write('/tmp/test_results.txt', output.join("\n"))
puts "Results written to /tmp/test_results.txt"
