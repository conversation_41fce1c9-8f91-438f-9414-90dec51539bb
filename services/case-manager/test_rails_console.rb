# Test script for Rails console - ActiveStruct Associations
puts "🧪 Testing ActiveStruct Associations in Rails Console"
puts "=" * 60

# Test 1: Check if our module is available
begin
  puts "✅ ActiveRecordAssociations module: #{Athar::Commons::ActiveStruct::ActiveRecordAssociations}"
rescue NameError => e
  puts "❌ Module not found: #{e.message}"
  exit 1
end

# Test 2: Check if RemoteUser model exists
begin
  puts "✅ RemoteUser model: #{RemoteUser}"
rescue NameError => e
  puts "❌ RemoteUser not found: #{e.message}"
  exit 1
end

# Test 3: Create a test ActiveRecord model with our association
class TestCaseModel < ApplicationRecord
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  self.table_name = 'cases'  # Use existing table
  
  belongs_to_active_struct :test_user, foreign_key: :assigned_user_id, class_name: 'RemoteUser'
end

puts "✅ Test model created with belongs_to_active_struct"

# Test 4: Check association metadata
associations = TestCaseModel.active_struct_associations
puts "✅ Association metadata: #{associations.key?(:test_user)}"

# Test 5: Create instance and test methods
test_case = TestCaseModel.new(assigned_user_id: 123)
puts "✅ Instance created with assigned_user_id: #{test_case.assigned_user_id}"

# Test 6: Check if methods exist
methods_exist = test_case.respond_to?(:test_user) && 
                test_case.respond_to?(:test_user=) && 
                test_case.respond_to?(:build_test_user)
puts "✅ Association methods exist: #{methods_exist}"

# Test 7: Test basic getter
begin
  user = test_case.test_user
  puts "✅ Basic getter works: #{user.class} with ID #{user.id}"
rescue => e
  puts "❌ Basic getter failed: #{e.message}"
end

# Test 8: Test setter with RemoteUser instance
begin
  remote_user = RemoteUser.new(id: 456, name: 'John Doe')
  test_case.test_user = remote_user
  puts "✅ Setter with RemoteUser: assigned_user_id = #{test_case.assigned_user_id}"
rescue => e
  puts "❌ Setter failed: #{e.message}"
end

# Test 9: Test nil assignment
begin
  test_case.test_user = nil
  puts "✅ Nil assignment: assigned_user_id = #{test_case.assigned_user_id.inspect}"
rescue => e
  puts "❌ Nil assignment failed: #{e.message}"
end

# Test 10: Test hash assignment
begin
  test_case.test_user = { id: 789, name: 'Jane Smith' }
  puts "✅ Hash assignment: assigned_user_id = #{test_case.assigned_user_id}"
rescue => e
  puts "❌ Hash assignment failed: #{e.message}"
end

# Test 11: Test build method
begin
  built_user = test_case.build_test_user(id: 999, name: 'Built User')
  puts "✅ Build method: #{built_user.class} with name '#{built_user.name}'"
rescue => e
  puts "❌ Build method failed: #{e.message}"
end

# Test 12: Test with actual Case model
begin
  puts "\n--- Testing with actual Case model ---"
  
  # Check if Case model exists
  puts "✅ Case model exists: #{Case}"
  
  # Create a case instance
  test_case_real = Case.new(assigned_user_id: 555)
  puts "✅ Case instance created with assigned_user_id: #{test_case_real.assigned_user_id}"
  
  # Test existing ActiveRpc methods
  if test_case_real.respond_to?(:assigned_user)
    puts "✅ Case already has assigned_user method (ActiveRpc)"
  end
  
rescue => e
  puts "❌ Case model test failed: #{e.message}"
end

# Test 13: Test RemoteUser associations
begin
  puts "\n--- Testing RemoteUser associations ---"
  
  remote_user = RemoteUser.new(id: 777, name: 'Test Manager')
  puts "✅ RemoteUser created: #{remote_user.name}"
  
  # Test if it has the expected methods
  methods = [:managed_cases, :created_cases, :case_comments, :uploaded_documents]
  methods.each do |method|
    if remote_user.respond_to?(method)
      puts "✅ RemoteUser has #{method} method"
    else
      puts "❌ RemoteUser missing #{method} method"
    end
  end
  
rescue => e
  puts "❌ RemoteUser test failed: #{e.message}"
end

puts "\n" + "=" * 60
puts "🎉 Rails Console Test Complete!"
puts "✅ ActiveStruct Associations working in Rails environment"
puts "✅ RemoteUser model functional"
puts "✅ Ready for full Case model integration"
puts "=" * 60
