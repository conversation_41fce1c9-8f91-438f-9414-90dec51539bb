puts "🔍 Debugging User Response Fields"
puts "=" * 50

user = RemoteUser.new(id: 1)

# Force fetch to see the raw response
begin
  user_data = user.send(:fetch_user_data)
  
  if user_data
    puts "✅ User data fetched successfully"
    puts "📋 Available methods on user_data:"
    
    # Get all methods that don't start with underscore and aren't basic Object methods
    available_methods = user_data.methods.reject { |m| 
      m.to_s.start_with?('_') || 
      Object.instance_methods.include?(m) ||
      m.to_s.include?('=')
    }.sort
    
    available_methods.each do |method|
      begin
        value = user_data.send(method)
        puts "   - #{method}: #{value.inspect}"
      rescue => e
        puts "   - #{method}: ERROR - #{e.message}"
      end
    end
    
    puts "\n📋 Raw user_data inspection:"
    puts user_data.inspect
    
  else
    puts "❌ No user data fetched"
  end
  
rescue => e
  puts "❌ Error fetching user data: #{e.message}"
end
