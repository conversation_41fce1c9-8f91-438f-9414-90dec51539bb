# Simple test script for Rails console - ActiveStruct Associations
puts "🧪 Testing ActiveStruct Associations in Rails Console"
puts "=" * 60

# Test 1: Check if our module is available
begin
  puts "✅ ActiveRecordAssociations module: #{Athar::Commons::ActiveStruct::ActiveRecordAssociations}"
rescue NameError => e
  puts "❌ Module not found: #{e.message}"
  exit 1
end

# Test 2: Create a simple ActiveStruct model for testing
class SimpleUser < Athar::Commons::ActiveStruct::Base
  attribute :id, :integer
  attribute :name, :string
  attribute :email, :string
end

puts "✅ SimpleUser ActiveStruct model created"

# Test 3: Create a test ActiveRecord model with our association
class TestModel < ApplicationRecord
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  self.table_name = 'form_submissions'  # Use existing table
  
  belongs_to_active_struct :test_user, foreign_key: :created_by_id, class_name: 'SimpleUser'
end

puts "✅ TestModel created with belongs_to_active_struct"

# Test 4: Check association metadata
associations = TestModel.active_struct_associations
puts "✅ Association metadata: #{associations.key?(:test_user)}"
puts "   - Type: #{associations[:test_user][:type]}"
puts "   - Foreign key: #{associations[:test_user][:foreign_key]}"
puts "   - Class name: #{associations[:test_user][:class_name]}"

# Test 5: Create instance and test methods
test_instance = TestModel.new(created_by_id: 123)
puts "✅ Instance created with created_by_id: #{test_instance.created_by_id}"

# Test 6: Check if methods exist
methods_exist = test_instance.respond_to?(:test_user) && 
                test_instance.respond_to?(:test_user=) && 
                test_instance.respond_to?(:build_test_user)
puts "✅ Association methods exist: #{methods_exist}"

# Test 7: Test basic getter
begin
  user = test_instance.test_user
  puts "✅ Basic getter works: #{user.class} with ID #{user.id}"
rescue => e
  puts "❌ Basic getter failed: #{e.message}"
end

# Test 8: Test setter with SimpleUser instance
begin
  simple_user = SimpleUser.new(id: 456, name: 'John Doe')
  test_instance.test_user = simple_user
  puts "✅ Setter with SimpleUser: created_by_id = #{test_instance.created_by_id}"
rescue => e
  puts "❌ Setter failed: #{e.message}"
end

# Test 9: Test nil assignment
begin
  test_instance.test_user = nil
  puts "✅ Nil assignment: created_by_id = #{test_instance.created_by_id.inspect}"
rescue => e
  puts "❌ Nil assignment failed: #{e.message}"
end

# Test 10: Test hash assignment
begin
  test_instance.test_user = { id: 789, name: 'Jane Smith' }
  puts "✅ Hash assignment: created_by_id = #{test_instance.created_by_id}"
  puts "   - Retrieved user name: #{test_instance.test_user.name}"
rescue => e
  puts "❌ Hash assignment failed: #{e.message}"
end

# Test 11: Test build method
begin
  built_user = test_instance.build_test_user(id: 999, name: 'Built User')
  puts "✅ Build method: #{built_user.class} with name '#{built_user.name}'"
  puts "   - Foreign key set to: #{test_instance.created_by_id}"
rescue => e
  puts "❌ Build method failed: #{e.message}"
end

# Test 12: Test caching
begin
  test_instance.created_by_id = 555
  user1 = test_instance.test_user
  user2 = test_instance.test_user
  puts "✅ Caching test: same object reference = #{user1.equal?(user2)}"
rescue => e
  puts "❌ Caching test failed: #{e.message}"
end

# Test 13: Test cache invalidation
begin
  test_instance.created_by_id = 666
  user3 = test_instance.test_user
  puts "✅ Cache invalidation: new object after FK change = #{!user1.equal?(user3)}"
  puts "   - New user ID: #{user3.id}"
rescue => e
  puts "❌ Cache invalidation test failed: #{e.message}"
end

# Test 14: Test class methods
begin
  puts "✅ Association check: #{TestModel.active_struct_association?(:test_user)}"
  puts "✅ All associations: #{TestModel.active_struct_associations.keys}"
rescue => e
  puts "❌ Class methods test failed: #{e.message}"
end

# Test 15: Test error handling
begin
  test_instance.test_user = "invalid"
  puts "❌ Error handling: should have raised error"
rescue ArgumentError => e
  puts "✅ Error handling: correctly rejected invalid type"
  puts "   - Error message: #{e.message}"
rescue => e
  puts "❌ Error handling failed: #{e.message}"
end

puts "\n" + "=" * 60
puts "🎉 Rails Console Test Complete!"
puts "✅ ActiveStruct Associations working in Rails environment"
puts "✅ All core functionality verified"
puts "✅ Ready for integration with actual Case model"
puts "=" * 60
