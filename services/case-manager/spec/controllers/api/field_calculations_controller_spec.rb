require 'rails_helper'

RSpec.describe Api::FieldCalculationsController, type: :controller do
  let(:user) { create(:user) }
  let(:form_template) { create(:form_template) }
  let(:form_section) { create(:form_section, form_template: form_template) }
  let(:calculated_field) do
    create(:form_field, 
           form_section: form_section,
           field_type: 'calculated',
           data_type: 'integer_data',
           calculation_config: {
             'calculation_type' => 'sum',
             'source_fields' => ['field1', 'field2']
           })
  end

  before do
    allow(controller).to receive(:authenticate_session!).and_return(true)
    allow(controller).to receive(:current_user).and_return(user)
    allow(controller).to receive(:authorize_resource!).and_return(true)
  end

  describe 'POST #calculate' do
    context 'with a calculated field' do
      it 'returns calculated value' do
        allow(FormFieldCalculationService).to receive(:calculate_field_value)
          .with(calculated_field, {})
          .and_return(42)

        post :calculate, params: { id: calculated_field.id, form_data: {} }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['field_id']).to eq(calculated_field.id)
        expect(json_response['value']).to eq(42)
      end
    end

    context 'with a non-calculated field' do
      let(:regular_field) { create(:form_field, form_section: form_section, field_type: 'text') }

      it 'returns unprocessable entity' do
        post :calculate, params: { id: regular_field.id }

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Field is not calculable')
      end
    end
  end

  describe 'POST #calculate_all_fields' do
    it 'calculates all fields for a form template' do
      calculated_values = {
        calculated_field.field_name => 42,
        'another_field' => 'calculated_value'
      }

      allow(FormFieldCalculationService).to receive(:calculate_all_fields)
        .with(form_template, {})
        .and_return(calculated_values)

      post :calculate_all_fields, params: { form_template_id: form_template.id, form_data: {} }

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['form_template_id']).to eq(form_template.id)
      expect(json_response['calculated_values']).to eq(calculated_values)
    end
  end

  describe 'POST #recalculate_dependent_fields' do
    it 'recalculates dependent fields' do
      calculated_values = { calculated_field.field_name => 42 }

      allow(FormFieldCalculationService).to receive(:recalculate_dependent_fields)
        .with('source_field', form_template, {})
        .and_return(calculated_values)

      post :recalculate_dependent_fields, params: {
        form_template_id: form_template.id,
        changed_field_name: 'source_field',
        form_data: {}
      }

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['form_template_id']).to eq(form_template.id)
      expect(json_response['changed_field']).to eq('source_field')
      expect(json_response['calculated_values']).to eq(calculated_values)
    end
  end
end
