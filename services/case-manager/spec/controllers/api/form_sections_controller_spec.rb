require 'rails_helper'

RSpec.describe Api::FormSectionsController, type: :controller do
  let(:user) { create(:user) }
  let(:form_template) { create(:form_template) }
  let(:form_section) { create(:form_section, form_template: form_template) }

  before do
    allow(controller).to receive(:authenticate_session!).and_return(true)
    allow(controller).to receive(:current_user).and_return(user)
    allow(controller).to receive(:authorize_resource!).and_return(true)
  end

  describe 'GET #index' do
    let!(:sections) { create_list(:form_section, 3, form_template: form_template) }

    it 'returns list of form sections' do
      allow(controller).to receive(:apply_filters).and_yield(FormSection.all)
      allow(controller).to receive(:paginate).and_return([sections, {}])
      allow(controller).to receive(:serialize_response)

      get :index

      expect(response).to have_http_status(:ok)
    end

    it 'applies filters correctly' do
      expect(controller).to receive(:apply_filters)
      get :index, params: { filter: { form_template_id: form_template.id } }
    end
  end

  describe 'GET #show' do
    it 'returns form section details' do
      allow(controller).to receive(:serialize_response)

      get :show, params: { id: form_section.id }

      expect(response).to have_http_status(:ok)
    end
  end
end
