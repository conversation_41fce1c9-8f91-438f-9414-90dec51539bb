require 'rails_helper'

RSpec.describe Api::FormFieldsController, type: :controller do
  let(:user) { create(:user) }
  let(:form_template) { create(:form_template) }
  let(:form_section) { create(:form_section, :form_section, form_template: form_template) }
  let(:list_section) { create(:form_section, :list_section, form_template: form_template) }
  let(:form_field) { create(:form_field, form_section: form_section) }
  let(:lookup_field) { create(:form_field, :lookup, form_section: form_section) }
  let(:calculated_field) { create(:form_field, :calculated, form_section: form_section) }
  let(:case_obj) { create(:case) }

  before do
    # Skip authorization callbacks for testing
    controller.class.skip_before_action :authenticate_session!, :authorize_form_field_access!

    allow(controller).to receive(:current_user).and_return(user)
    allow(controller).to receive(:current_project).and_return(double(id: 24))
  end

  describe 'GET #index' do
    context 'without form_section_id' do
      it 'returns paginated form fields' do
        form_field
        get :index
        
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')
      end

      it 'filters by field_type' do
        text_field = create(:form_field, form_section: form_section, field_type: 'text')
        number_field = create(:form_field, form_section: form_section, field_type: 'number')
        
        get :index, params: { field_type: 'text' }
        
        expect(response).to have_http_status(:success)
      end
    end

    context 'with form_section_id for form-type section' do
      it 'returns fields for the section' do
        form_field
        get :index, params: { form_section_id: form_section.id }
        
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')
      end

      it 'includes context-aware field states when case_id provided' do
        form_field
        get :index, params: { 
          form_section_id: form_section.id, 
          case_id: case_obj.id 
        }
        
        expect(response).to have_http_status(:success)
      end
    end

    context 'with form_section_id for list-type section' do
      it 'returns sub-form management data' do
        get :index, params: { form_section_id: list_section.id }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['data']['section_type']).to eq('list')
        expect(json_response['data']['sub_form_template_name']).to eq('service_instance')
      end

      it 'includes existing sub-submissions when case_id provided' do
        # Create a mock submission for the list section
        allow(list_section).to receive(:submissions_for_case).and_return([])
        
        get :index, params: { 
          form_section_id: list_section.id, 
          case_id: case_obj.id 
        }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['data']['current_count']).to eq(0)
        expect(json_response['data']['can_add_more']).to be_truthy
      end
    end
  end

  describe 'GET #lookup_options' do
    it 'returns lookup options for lookup fields' do
      get :lookup_options, params: { id: lookup_field.id }
      
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      expect(json_response['field_id']).to eq(lookup_field.id)
      expect(json_response['lookup_source_type']).to eq(lookup_field.lookup_source_type)
    end

    it 'returns error for non-lookup fields' do
      get :lookup_options, params: { id: form_field.id }
      
      expect(response).to have_http_status(:unprocessable_entity)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to eq('Field is not a lookup field')
    end

    it 'supports search filtering' do
      get :lookup_options, params: { 
        id: lookup_field.id, 
        search: 'test' 
      }
      
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      expect(json_response['searched']).to be_truthy
      expect(json_response['search_term']).to eq('test')
    end

    it 'supports limit parameter' do
      get :lookup_options, params: { 
        id: lookup_field.id, 
        limit: 10 
      }
      
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #calculate' do
    it 'calculates field value for calculated fields' do
      post :calculate, params: { 
        id: calculated_field.id,
        value: '10',
        form_data: { 'field1' => '5', 'field2' => '5' }
      }
      
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      expect(json_response['field_id']).to eq(calculated_field.id)
      expect(json_response['field_name']).to eq(calculated_field.field_name)
    end

    it 'returns error for non-calculated fields' do
      post :calculate, params: { 
        id: form_field.id,
        value: '10'
      }
      
      expect(response).to have_http_status(:unprocessable_entity)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to eq('Field is not calculable')
    end
  end

  describe 'private methods' do
    describe '#build_context' do
      it 'builds context from parameters' do
        controller.params = { 
          case_id: case_obj.id,
          form_submission_id: '123'
        }
        
        context = controller.send(:build_context, controller.params)
        
        expect(context[:case]).to eq(case_obj)
        expect(context[:project_id]).to eq(24)
      end
    end
  end
end
