FactoryBot.define do
  factory :form_template do
    sequence(:name) { |n| "form_template_#{n}" }
    sequence(:title) { |n| "Form Template #{n}" }
    description { 'Test form template' }
    sequence(:sequence_order)
    target_role { 'both' }
    active { true }
    project_id { 24 }
  end

  factory :form_section do
    association :form_template
    sequence(:name) { |n| "section_#{n}" }
    sequence(:title) { |n| "Section #{n}" }
    description { 'Test form section' }
    sequence(:display_order)
    is_required { true }
    visible { true }
    section_type { 'list' }  # Default to list to avoid validation issues
    sub_form_template_name { 'service_instance' }
    sub_form_button_label { 'Add Service +' }
    max_sub_forms { 10 }

    trait :list_section do
      section_type { 'list' }
      sub_form_template_name { 'service_instance' }
      sub_form_button_label { 'Add Service +' }
      max_sub_forms { 10 }
    end

    trait :form_section do
      section_type { 'form' }
      sub_form_template_name { nil }
      sub_form_button_label { nil }
      max_sub_forms { nil }

      # Create a form field before validation to satisfy the presence validation
      after(:build) do |section|
        section.form_fields.build(
          field_name: 'default_field',
          label: 'Default Field',
          field_type: 'text',
          data_type: 'string_data',
          display_order: 999,  # Use high number to avoid conflicts
          required: false,
          visible: true
        )
      end
    end
  end

  factory :form_field do
    association :form_section
    sequence(:field_name) { |n| "field_#{n}" }
    sequence(:label) { |n| "Field #{n}" }
    field_type { 'text' }
    data_type { 'string_data' }
    sequence(:display_order)
    required { false }
    visible { true }
    help_text { 'Test help text' }
    placeholder { 'Enter value' }

    trait :calculated do
      field_type { 'calculated' }
      calculation_formula { 'sum' }
      depends_on { [ 'field1', 'field2' ].to_json }
      auto_calculate { true }
    end

    trait :lookup do
      field_type { 'lookup' }
      lookup_source_type { 'StaffPosition' }
    end

    trait :required_field do
      required { true }
    end
  end

  factory :form_field_option do
    association :form_field
    sequence(:option_key) { |n| "option_#{n}" }
    sequence(:option_value) { |n| "Option #{n}" }
    sequence(:display_order)
    active { true }
  end

  factory :form_field_validation do
    association :form_field
    validation_type { 'required' }
    validation_value { 'true' }
    error_message { 'This field is required' }
    active { true }
    sequence(:display_order)
  end

  factory :form_submission do
    association :form_template
    association :case
    created_by_id { SecureRandom.uuid }
    status { 'form_draft' }
    form_data { {} }
  end
end
