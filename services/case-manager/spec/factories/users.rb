FactoryBot.define do
  # Factory for AtharAuth::Models::User (from auth gem)
  factory :auth_user, class: 'AtharAuth::Models::User' do
    skip_create # This is a value object, not an ActiveRecord model

    id { 1 }
    name { Faker::Name.name }
    email { Faker::Internet.email }
    global { false }
    user_type { "project_based" }

    trait :case_manager_user do
      name { "Case Manager " + Faker::Name.name }
      email { "cm+" + Faker::Internet.email }
    end

    trait :admin_user do
      name { "Admin " + Faker::Name.name }
      email { "admin+" + Faker::Internet.email }
      global { true }
      user_type { "global" }
    end

    trait :supervisor_user do
      name { "Supervisor " + Faker::Name.name }
      email { "supervisor+" + Faker::Internet.email }
    end

    initialize_with { new(attributes) }
  end

  # Alias for backward compatibility
  factory :user, parent: :auth_user
end
