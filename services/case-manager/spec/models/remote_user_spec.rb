# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RemoteUser, type: :model do
  let(:user_attributes) do
    {
      id: 123,
      name: '<PERSON>',
      email: '<EMAIL>'
    }
  end

  let(:remote_user) { RemoteUser.new(user_attributes) }

  describe 'inheritance' do
    it 'inherits from AtharAuth::Models::User' do
      expect(RemoteUser.superclass).to eq(AtharAuth::Models::User)
    end
  end

  describe 'associations' do
    it 'defines has_many associations for cases' do
      expect(remote_user).to respond_to(:managed_cases)
      expect(remote_user).to respond_to(:created_cases)
      expect(remote_user).to respond_to(:case_comments)
      expect(remote_user).to respond_to(:uploaded_documents)
    end
  end

  describe '#active_cases' do
    before do
      # Mock the managed_cases association
      allow(remote_user).to receive(:managed_cases).and_return(
        double('CaseRelation', where: [
          double('Case', status: 'active'),
          double('Case', status: 'approved')
        ])
      )
    end

    it 'returns cases with active or approved status' do
      active_cases = remote_user.active_cases
      expect(active_cases).to be_an(Array)
      expect(active_cases.length).to eq(2)
    end
  end

  describe '#case_load_count' do
    before do
      in_progress_relation = double('CaseRelation', count: 5)
      managed_cases_relation = double('CaseRelation', in_progress: in_progress_relation)
      allow(remote_user).to receive(:managed_cases).and_return(managed_cases_relation)
    end

    it 'returns count of in-progress cases' do
      expect(remote_user.case_load_count).to eq(5)
    end
  end

  describe '#pending_cases' do
    before do
      allow(remote_user).to receive(:managed_cases).and_return(
        double('CaseRelation', where: [
          double('Case', status: 'draft'),
          double('Case', status: 'ready_for_approval')
        ])
      )
    end

    it 'returns cases with pending statuses' do
      pending_cases = remote_user.pending_cases
      expect(pending_cases).to be_an(Array)
      expect(pending_cases.length).to eq(2)
    end
  end

  describe '#closed_cases' do
    before do
      allow(remote_user).to receive(:managed_cases).and_return(
        double('CaseRelation', where: [double('Case', status: 'closed')])
      )
    end

    it 'returns closed cases' do
      closed_cases = remote_user.closed_cases
      expect(closed_cases).to be_an(Array)
      expect(closed_cases.length).to eq(1)
    end
  end

  describe '#can_manage_cases?' do
    context 'when user is a case manager' do
      before { allow(remote_user).to receive(:case_manager?).and_return(true) }

      it 'returns true' do
        expect(remote_user.can_manage_cases?).to be true
      end
    end

    context 'when user is a supervisor' do
      before do
        allow(remote_user).to receive(:case_manager?).and_return(false)
        allow(remote_user).to receive(:supervisor?).and_return(true)
      end

      it 'returns true' do
        expect(remote_user.can_manage_cases?).to be true
      end
    end

    context 'when user is an admin' do
      before do
        allow(remote_user).to receive(:case_manager?).and_return(false)
        allow(remote_user).to receive(:supervisor?).and_return(false)
        allow(remote_user).to receive(:admin?).and_return(true)
      end

      it 'returns true' do
        expect(remote_user.can_manage_cases?).to be true
      end
    end

    context 'when user has no management roles' do
      before do
        allow(remote_user).to receive(:case_manager?).and_return(false)
        allow(remote_user).to receive(:supervisor?).and_return(false)
        allow(remote_user).to receive(:admin?).and_return(false)
      end

      it 'returns false' do
        expect(remote_user.can_manage_cases?).to be false
      end
    end
  end

  describe '#display_name' do
    context 'when name is present' do
      it 'returns the name' do
        expect(remote_user.display_name).to eq('John Doe')
      end
    end

    context 'when name is blank' do
      let(:remote_user) { RemoteUser.new(id: 123, name: '') }

      it 'returns a fallback with user ID' do
        expect(remote_user.display_name).to eq('User #123')
      end
    end
  end

  describe '#avatar_url_with_fallback' do
    context 'when avatar_url is present' do
      before { allow(remote_user).to receive(:avatar_url).and_return('https://example.com/avatar.jpg') }

      it 'returns the avatar URL' do
        expect(remote_user.avatar_url_with_fallback).to eq('https://example.com/avatar.jpg')
      end
    end

    context 'when avatar_url is blank' do
      before { allow(remote_user).to receive(:avatar_url).and_return(nil) }

      it 'returns the default avatar path' do
        expect(remote_user.avatar_url_with_fallback).to eq('/assets/default-avatar.png')
      end
    end
  end

  describe 'initialization' do
    it 'accepts standard user attributes' do
      expect(remote_user.id).to eq(123)
      expect(remote_user.name).to eq('John Doe')
      expect(remote_user.email).to eq('<EMAIL>')
    end

    it 'inherits all AtharAuth::Models::User functionality' do
      # Test that it has the expected methods from the parent class
      expect(remote_user).to respond_to(:global_user?)
      expect(remote_user).to respond_to(:project_based_user?)
      expect(remote_user).to respond_to(:case_manager?)
      expect(remote_user).to respond_to(:supervisor?)
      expect(remote_user).to respond_to(:admin?)
    end
  end
end
