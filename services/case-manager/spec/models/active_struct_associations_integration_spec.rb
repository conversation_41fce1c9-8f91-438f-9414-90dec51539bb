# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'ActiveStruct Associations Integration', type: :model do
  # Create a simple test ActiveStruct model that doesn't require database connections
  let(:test_user_class) do
    Class.new(Athar::Commons::ActiveStruct::Base) do
      attribute :name, :string
      attribute :email, :string
      
      def self.name
        'TestUser'
      end
    end
  end

  # Create a simple test ActiveRecord model
  let(:test_case_class) do
    Class.new(ApplicationRecord) do
      include Athar::Commons::ActiveStruct::ActiveRecordAssociations
      
      self.table_name = 'cases'  # Use existing table
      
      belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'TestUser'
      belongs_to_active_struct :created_by_user, foreign_key: :created_by_id, class_name: 'TestUser'
      
      def self.name
        'TestCase'
      end
    end
  end

  before do
    stub_const('TestUser', test_user_class)
    stub_const('TestCase', test_case_class)
  end

  describe 'basic functionality' do
    let(:test_case) { test_case_class.new(assigned_user_id: 123) }

    it 'includes the ActiveRecordAssociations module' do
      expect(test_case_class.included_modules).to include(Athar::Commons::ActiveStruct::ActiveRecordAssociations)
    end

    it 'defines association metadata' do
      associations = test_case_class.active_struct_associations
      
      expect(associations).to have_key(:assigned_user)
      expect(associations[:assigned_user]).to include(
        type: :belongs_to_active_struct,
        foreign_key: :assigned_user_id,
        class_name: 'TestUser'
      )
    end

    it 'creates association methods' do
      expect(test_case).to respond_to(:assigned_user)
      expect(test_case).to respond_to(:assigned_user=)
      expect(test_case).to respond_to(:build_assigned_user)
    end

    it 'can create ActiveStruct instances' do
      user = test_case.assigned_user
      
      expect(user).to be_a(TestUser)
      expect(user.id).to eq(123)
    end

    it 'can set associations' do
      user = TestUser.new(id: 456, name: 'John Doe')
      test_case.assigned_user = user
      
      expect(test_case.assigned_user_id).to eq(456)
      expect(test_case.assigned_user).to be(user)
    end

    it 'can build associations' do
      user = test_case.build_assigned_user(id: 789, name: 'Built User')
      
      expect(user).to be_a(TestUser)
      expect(user.name).to eq('Built User')
      expect(test_case.assigned_user).to be(user)
    end

    it 'caches association results' do
      user1 = test_case.assigned_user
      user2 = test_case.assigned_user
      
      expect(user1).to be(user2) # Same object reference
    end

    it 'invalidates cache when foreign key changes' do
      user1 = test_case.assigned_user
      test_case.assigned_user_id = 999
      user2 = test_case.assigned_user
      
      expect(user1).not_to be(user2)
      expect(user2.id).to eq(999)
    end
  end

  describe 'class methods' do
    it 'can check if association exists' do
      expect(test_case_class.active_struct_association?(:assigned_user)).to be true
      expect(test_case_class.active_struct_association?(:nonexistent)).to be false
    end

    it 'returns all associations' do
      associations = test_case_class.active_struct_associations
      expect(associations.keys).to include(:assigned_user, :created_by_user)
    end
  end

  describe 'instance methods' do
    let(:test_case) { test_case_class.new(assigned_user_id: 123, created_by_id: 456) }

    it 'can clear association cache' do
      # Load associations
      test_case.assigned_user
      test_case.created_by_user
      
      expect(test_case.active_struct_association_cached?(:assigned_user)).to be true
      
      test_case.clear_active_struct_association_cache
      
      expect(test_case.active_struct_association_cached?(:assigned_user)).to be false
    end

    it 'can reload specific associations' do
      user1 = test_case.assigned_user
      user2 = test_case.reload_active_struct_association(:assigned_user)
      
      expect(user1).not_to be(user2)
      expect(user2.id).to eq(123)
    end
  end

  describe 'error handling' do
    let(:test_case) { test_case_class.new }

    it 'handles nil assignments' do
      test_case.assigned_user = nil
      
      expect(test_case.assigned_user_id).to be_nil
      expect(test_case.assigned_user).to be_nil
    end

    it 'raises error for invalid assignments' do
      expect {
        test_case.assigned_user = "invalid"
      }.to raise_error(ArgumentError, /Expected TestUser instance, Hash, or nil/)
    end

    it 'handles hash assignments' do
      test_case.assigned_user = { id: 555, name: 'Hash User' }
      
      expect(test_case.assigned_user_id).to eq(555)
      expect(test_case.assigned_user.name).to eq('Hash User')
    end
  end

  describe 'ActiveRpc integration simulation' do
    let(:test_case_with_rpc) do
      Class.new(ApplicationRecord) do
        include Athar::Commons::ActiveStruct::ActiveRecordAssociations
        
        self.table_name = 'cases'
        
        belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'TestUser'
        
        # Simulate ActiveRpc method
        def assigned_user_data
          if assigned_user_id.present?
            double('RpcResponse', message: TestUser.new(
              id: assigned_user_id,
              name: 'RPC User',
              email: '<EMAIL>'
            ))
          end
        end
        
        def self.name
          'TestCaseWithRpc'
        end
      end
    end

    it 'uses ActiveRpc method when available' do
      stub_const('TestCaseWithRpc', test_case_with_rpc)
      test_case = test_case_with_rpc.new(assigned_user_id: 123)
      
      user = test_case.assigned_user
      
      expect(user).to be_a(TestUser)
      expect(user.name).to eq('RPC User')
      expect(user.email).to eq('<EMAIL>')
    end
  end
end
