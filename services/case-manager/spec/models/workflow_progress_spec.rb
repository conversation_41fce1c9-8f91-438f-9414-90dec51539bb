require 'rails_helper'

RSpec.describe WorkflowProgress, type: :model do
  let(:form_template) { create(:form_template) }
  let(:case_id) { SecureRandom.uuid }
  let(:workflow_progress) { create(:workflow_progress, case_id: case_id, form_template: form_template) }

  describe 'associations' do
    it { should belong_to(:form_template) }
  end

  describe 'validations' do
    it { should validate_presence_of(:case_id) }
    it { should validate_presence_of(:completion_percentage) }
    it { should validate_presence_of(:sections_completed) }
    it { should validate_presence_of(:total_sections) }

    it { should validate_numericality_of(:completion_percentage).is_greater_than_or_equal_to(0).is_less_than_or_equal_to(100) }
    it { should validate_numericality_of(:sections_completed).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:total_sections).is_greater_than_or_equal_to(0) }

    it { should validate_uniqueness_of(:case_id).scoped_to(:form_template_id) }
  end

  describe 'scopes' do
    let!(:completed_progress) { create(:workflow_progress, completion_percentage: 100.0) }
    let!(:in_progress_progress) { create(:workflow_progress, completion_percentage: 50.0) }
    let!(:not_started_progress) { create(:workflow_progress, completion_percentage: 0.0) }

    it 'filters completed progress' do
      expect(WorkflowProgress.completed).to include(completed_progress)
      expect(WorkflowProgress.completed).not_to include(in_progress_progress)
    end

    it 'filters in progress' do
      expect(WorkflowProgress.in_progress).to include(in_progress_progress)
      expect(WorkflowProgress.in_progress).not_to include(completed_progress)
    end

    it 'filters not started' do
      expect(WorkflowProgress.not_started).to include(not_started_progress)
      expect(WorkflowProgress.not_started).not_to include(in_progress_progress)
    end

    it 'filters by case' do
      expect(WorkflowProgress.for_case(case_id)).to include(workflow_progress)
    end
  end

  describe 'class methods' do
    describe '.calculate_for_case_and_template' do
      it 'creates or updates progress for case and template' do
        expect {
          WorkflowProgress.calculate_for_case_and_template(case_id, form_template)
        }.to change(WorkflowProgress, :count).by(1)
      end

      it 'updates existing progress' do
        existing_progress = create(:workflow_progress, case_id: case_id, form_template: form_template)

        result = WorkflowProgress.calculate_for_case_and_template(case_id, form_template)

        expect(result.id).to eq(existing_progress.id)
      end
    end
  end

  describe 'instance methods' do
    describe '#completed?' do
      it 'returns true when completion is 100%' do
        workflow_progress.completion_percentage = 100.0
        expect(workflow_progress.completed?).to be_truthy
      end

      it 'returns false when completion is less than 100%' do
        workflow_progress.completion_percentage = 99.9
        expect(workflow_progress.completed?).to be_falsey
      end
    end

    describe '#in_progress?' do
      it 'returns true when completion is between 0 and 100' do
        workflow_progress.completion_percentage = 50.0
        expect(workflow_progress.in_progress?).to be_truthy
      end

      it 'returns false when completion is 0 or 100' do
        workflow_progress.completion_percentage = 0.0
        expect(workflow_progress.in_progress?).to be_falsey

        workflow_progress.completion_percentage = 100.0
        expect(workflow_progress.in_progress?).to be_falsey
      end
    end

    describe '#not_started?' do
      it 'returns true when completion is 0%' do
        workflow_progress.completion_percentage = 0.0
        expect(workflow_progress.not_started?).to be_truthy
      end

      it 'returns false when completion is greater than 0%' do
        workflow_progress.completion_percentage = 0.1
        expect(workflow_progress.not_started?).to be_falsey
      end
    end

    describe '#progress_summary' do
      it 'returns comprehensive progress data' do
        summary = workflow_progress.progress_summary

        expect(summary[:case_id]).to eq(case_id)
        expect(summary[:form_template][:id]).to eq(form_template.id)
        expect(summary[:completion_percentage]).to eq(workflow_progress.completion_percentage)
        expect(summary[:status]).to be_present
      end
    end

    describe '#progress_status' do
      it 'returns correct status based on completion' do
        workflow_progress.completion_percentage = 0.0
        expect(workflow_progress.progress_status).to eq('not_started')

        workflow_progress.completion_percentage = 50.0
        expect(workflow_progress.progress_status).to eq('in_progress')

        workflow_progress.completion_percentage = 100.0
        expect(workflow_progress.progress_status).to eq('completed')
      end
    end
  end
end
