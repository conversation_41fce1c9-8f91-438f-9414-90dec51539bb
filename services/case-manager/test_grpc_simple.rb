Rails.logger.info "=== GRPC TEST STARTING ==="

begin
  Rails.logger.info "Creating RemoteUser with ID 193"
  user = RemoteUser.new(id: 193)
  
  Rails.logger.info "Calling user.name to trigger GRPC fetch"
  name = user.name
  
  Rails.logger.info "GRPC call completed. Name: #{name}"
  
rescue => e
  Rails.logger.error "GRPC test failed: #{e.message}"
  Rails.logger.error "Error class: #{e.class}"
  Rails.logger.error "Backtrace: #{e.backtrace.first(3).join(' | ')}"
end

Rails.logger.info "=== GRPC TEST COMPLETED ==="
