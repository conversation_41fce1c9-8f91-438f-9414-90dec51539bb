# CHECKPOINT: RemoteUser model before ActiveRpc configuration
# Date: 2025-01-28
# Status: rpc_belongs_to implemented, but <PERSON><PERSON><PERSON><PERSON> missing active_rpc config

class RemoteUser < AtharAuth::Models::User
  include AtharRpc::ActiveRpc

  # Configure gRPC integration to fetch user data from Core service
  attribute :name, :string
  attribute :email, :string
  # attribute :user_roles_list, :user_role_collection, default: []
  # attribute :avatar_attributes, :avatar_attributes_type, default: {}
  attribute :status, :string

  # Case-manager specific associations
  has_many :managed_cases, class_name: "Case", foreign_key: :assigned_user_id, primary_key: :id
  has_many :created_cases, class_name: "Case", foreign_key: :created_by_id, primary_key: :id
  has_many :case_comments, class_name: "Comment", foreign_key: :user_id, primary_key: :id
  has_many :uploaded_documents, class_name: "CaseDocument", foreign_key: :uploaded_by_id, primary_key: :id

  # Case-manager specific methods
  def active_cases
    managed_cases.where(status: [ 'active', 'approved' ])
  end

  def case_load_count
    managed_cases.in_progress.count
  end

  def pending_cases
    managed_cases.where(status: [ 'draft', 'ready_for_approval', 'pending_approval' ])
  end

  def closed_cases
    managed_cases.where(status: 'closed')
  end

  # Helper method to check if user can manage cases
  def can_manage_cases?
    case_manager? || supervisor? || admin?
  end

  # Display name for UI
  def display_name
    name.present? ? name : "User ##{id}"
  end

  # Avatar URL with fallback
  def avatar_url_with_fallback
    avatar_url.presence || "/assets/default-avatar.png"
  end
end

# ISSUE: Missing active_rpc configuration block
# RESULT: Case.first.assigned_user returns RemoteUser with nil attributes
# NEXT: Add active_rpc configuration to enable GRPC fetching
