# AtharCM Form System API Improvement Plan

## Executive Summary

This document outlines a comprehensive plan to improve the AtharCM Form System APIs to better align with Figma designs, standardize response formats, and enhance frontend development experience. The plan replaces the monolithic `render_data` approach with granular, progressive loading APIs that provide better performance and user experience.

**NEW: Dynamic Sub-Form Architecture** - This plan now includes a revolutionary dynamic sub-form system that transforms modal dialogs (like the Figma service dialog "خطط التدخل وتفاصيل الخدمات") into reusable form templates with hierarchical FormSubmissions, enabling services, follow-ups, and any future modal forms to be managed through the same infrastructure.

## Current State Analysis

### Existing API Structure

- **Form Templates API**: ✅ Basic listing + `render_data` (monolithic - needs removal)
- **Form Sections API**: ✅ **COMPREHENSIVE** - JSONAPI format with serializers
- **Form Submissions API**: ✅ **COMPREHENSIVE** - Auto-creation lifecycle, status progression
- **Field Calculations API**: ✅ **COMPREHENSIVE** - FieldCalculationsController with individual field calculation endpoint
- **Form Validation API**: ✅ **COMPREHENSIVE** - Fully implemented validation system
- **Form Fields API**: ❌ **MISSING** - No FormFieldsController exists, only calculation routes handled by FieldCalculationsController
- **Problem**: `render_data` endpoint loads ALL form data at once (inefficient)
- **Missing**: Context-aware field loading with conditional logic
- **NEW REQUIREMENT**: Dynamic sub-form system for modal dialogs (services, follow-ups, etc.)

### Response Format Inconsistencies

| Endpoint                              | Current Format | Serializer Used       | Status                                                      |
| ------------------------------------- | -------------- | --------------------- | ----------------------------------------------------------- |
| `/api/form_templates/:id/render_data` | Raw JSON       | None                  | **TO BE REMOVED** - Monolithic approach                     |
| `/api/form_sections`                  | JSONAPI        | FormSectionSerializer | ✅ **COMPREHENSIVE** - Fully implemented                    |
| `/api/form_fields`                    | N/A            | N/A                   | ❌ **MISSING** - No FormFieldsController exists             |
| `/api/form_fields/:id/calculate`      | JSON           | None                  | ✅ **IMPLEMENTED** - Handled by FieldCalculationsController |

### Agreed UI Flow Requirements

Based on analysis and user feedback, the frontend needs:

1. **Progressive Loading**: Load only what's needed when needed
2. **Granular Access**: Individual section and field access
3. **Context-Aware Fields**: Field states based on current form data
4. **Real-time Conditions**: Server-side calculation of field visibility/enabled state
5. **Simple Refresh**: Re-fetch section fields after saves
6. **Dynamic Sub-Forms**: Modal dialogs that are actual form templates with hierarchical submissions
7. **Reusable Modal System**: Same infrastructure for services, follow-ups, family members, incidents, etc.

## **Comprehensive UI Flow Examples**

### **1. Case Creation & Opening Flow**

**For New Cases:**

```
1. User starts new case → Navigate to first form template (e.g., "consent_assent")
2. Load first template sections → GET /api/form_sections?template_id=:first_template_id
3. User fills first form with case + beneficiary data
4. Submit first form → POST /api/form_submissions (creates case AND form submission together)
5. Load case progress → GET /api/cases/:new_case_id/progress
6. Load ALL workflow templates → GET /api/form_templates?filter[template_type]=workflow
7. Render left navigation with all templates + completion status
8. Continue with progressive form flow
```

**For Existing Cases:**

```
1. Load cases list → GET /api/cases
2. Select case → GET /api/cases/:id (includes basic case info)
3. Load case progress → GET /api/cases/:id/progress (cached, fast response)
4. Load ALL workflow templates → GET /api/form_templates?filter[template_type]=workflow
5. Render left navigation with all templates + completion status
6. User selects template from navigation (or auto-select current incomplete one)
7. Load selected template sections → GET /api/form_sections?template_id=:selected_template_id
8. Load existing submissions for this template → GET /api/form_submissions?case_id=:id&template_id=:selected_template_id
9. Render form sections based on section_type ('form' vs 'list')
```

### **2. Form Section Rendering Flow**

**For `section_type: 'form'` (Inline Forms):**

```
1. Load section fields → GET /api/form_sections/:id/form_fields?case_id=:case_id
2. Load existing submission → Find submission for this section from step 8 above
3. Render fields inline using FormRenderer
4. Auto-save on field change → PATCH /api/form_submissions/:id
5. Status updates automatically via FormSubmission::StatusTracking concern
6. Left navigation updates with completion checkmarks ✓
```

**For `section_type: 'list'` (Modal Collections):**

```
1. Load section config → Already loaded in step 7 above
2. Load existing items → GET /api/form_submissions/:id/section_submissions?section_id=:id
3. Render item list + "Add +" button
4. On "Add +" click → Load component template → GET /api/form_templates/:component_template_id
5. Open modal with component form
6. Save new item → POST /api/form_submissions/component_submissions
7. Refresh item list → Re-fetch step 2
8. Progress updates automatically via WebSocket
9. Left navigation updates with completion status
```

### **3. Left Navigation & Template Switching Flow (Based on Figma)**

```
User Story: Case manager navigates between workflow templates

Initial Load:
1. GET /api/form_templates?filter[template_type]=workflow returns:
   [
     { id: 1, name: "consent_assent", title: "التعرف على الحالة", sequence: 1 },
     { id: 2, name: "case_plan", title: "خطة الحالة", sequence: 2 },
     { id: 3, name: "assessment", title: "حالة التقييم", sequence: 3 },
     { id: 4, name: "protection_followup", title: "متابعاتها الحماية", sequence: 4 },
     { id: 5, name: "services_followup", title: "المتابعة والخدمات", sequence: 5 },
     { id: 6, name: "case_closure", title: "إغلاق الحالة", sequence: 6 }
   ]

2. GET /api/cases/123/progress returns completion status for each template

3. Render left navigation with:
   - Template titles in Arabic
   - Completion checkmarks ✓ for completed templates
   - Current template highlighted
   - Disabled state for templates that can't be accessed yet

User clicks different template:
4. Load new template sections → GET /api/form_sections?template_id=:new_template_id
5. Load submissions for new template → GET /api/form_submissions?case_id=123&template_id=:new_template_id
6. Re-render main content area with new template's sections
7. Update URL/state to reflect current template
8. Left navigation updates active state
```

### **4. Service Management Flow (Figma Dialog)**

```
User Story: Case manager wants to add a new service intervention

1. Navigate to "المتابعة والخدمات" template (sequence: 3)
2. See "الخدمات" section (section_type: 'list')
3. View existing services list
4. Click "إضافة خدمة +" button
   ↓
5. Load service component template:
   GET /api/form_templates?filter[name]=service_instance&template_type=component
   ↓
6. Open modal with service form fields:
   - نوع الخدمة (Service Type)
   - تاريخ البداية (Start Date)
   - تاريخ النهاية (End Date)
   - الهدف من الخدمة (Service Goal)
   - ملاحظات (Notes)
   ↓
7. User fills form and clicks "حفظ"
   POST /api/form_submissions/component_submissions
   {
     "case_id": 123,
     "form_section_id": 456, // service_details section
     "form_data": {
       "service_type": "counseling",
       "start_date": "2024-01-15",
       "end_date": "2024-03-15",
       "service_goal": "Improve coping skills",
       "notes": "Weekly sessions"
     }
   }
   ↓
8. Modal closes, services list refreshes
9. Progress updates via WebSocket (services section now has 1+ items)
10. Template progress recalculated in background
```

### **4. Follow-up Management Flow**

```
User Story: Case manager wants to schedule follow-up visits

1. In same "المتابعة والخدمات" template
2. See "المتابعة" section (section_type: 'list')
3. View existing follow-ups
4. Click "إضافة متابعة +" button
   ↓
5. Load follow-up component template:
   GET /api/form_templates?filter[name]=followup_instance&template_type=component
   ↓
6. Open modal with follow-up form:
   - نوع المتابعة (Follow-up Type)
   - تاريخ المتابعة (Follow-up Date)
   - طريقة المتابعة (Method: phone, visit, etc.)
   - النتائج (Results)
   - الإجراءات المطلوبة (Required Actions)
   ↓
7. Save follow-up:
   POST /api/form_submissions/component_submissions
   {
     "case_id": 123,
     "form_section_id": 789, // followup_details section
     "form_data": {
       "followup_type": "home_visit",
       "followup_date": "2024-01-20",
       "method": "in_person",
       "results": "Family situation improved",
       "required_actions": "Continue current plan"
     }
   }
   ↓
8. Follow-ups list updates
9. Section completion rules check (FollowupsListCompletionRule returns true - optional)
```

### **5. Template Navigation Flow**

```
User Story: Case manager completes current template and moves to next

1. User completes all required sections in current template
2. FormSection completion rules trigger:
   - Form sections: Check required fields complete
   - List sections: Check minimum requirements (e.g., 1+ services)
   ↓
3. Template completion calculated:
   FormTemplate::ProgressCalculation checks all sections complete
   ↓
4. Case workflow progress updates:
   Case::WorkflowProgress concern triggers background job
   ↓
5. RefreshWorkflowProgressJob runs:
   - Finds next incomplete template
   - Updates WorkflowProgress cache
   - Broadcasts update via WebSocket
   ↓
6. Frontend receives progress update:
   - Navigation updates to show next template available
   - Progress bars update
   - User sees notification: "Template completed! Next: التقييم الشامل"
   ↓
7. User clicks next template:
   - Loads new template sections
   - Renders appropriate form/list sections
   - Cycle repeats
```

### **6. Real-time Progress Updates Flow**

```
Multiple users working on same case:

User A adds service:
1. POST /api/form_submissions/component_submissions
2. FormSubmission::StatusTracking marks submission complete
3. Case::WorkflowProgress invalidates cache
4. Background job recalculates progress
5. WebSocket broadcasts to all case subscribers
   ↓
User B sees real-time update:
6. CaseProgressTracker component receives WebSocket message
7. Progress bars update automatically
8. Notification: "Case progress updated by User A"
9. Services list refreshes if User B is viewing same section
```

### **7. Error Handling & Validation Flow**

```
User submits invalid service data:

1. POST /api/form_submissions/component_submissions (invalid data)
2. FormSubmission validation fails
3. API returns 422 with detailed errors:
   {
     "errors": {
       "form_data.start_date": ["cannot be in the future"],
       "form_data.service_type": ["is required"]
     }
   }
4. Modal shows field-specific error messages
5. User corrects errors and resubmits
6. Success flow continues from step 7 of Service Management Flow
```

### **8. Assessment Form Flow (Traditional Form Section)**

```
User Story: Case manager fills comprehensive assessment

1. Navigate to "التقييم الشامل" template (sequence: 4)
2. See "assessment_basic" section (section_type: 'form')
3. Load assessment fields:
   GET /api/form_sections/:assessment_section_id/form_fields?case_id=123
   ↓
4. Render fields inline:
   - Risk assessment questions
   - Family dynamics evaluation
   - Child development milestones
   - Safety concerns checklist
   ↓
5. User fills fields with auto-save:
   PATCH /api/form_submissions/:assessment_submission_id
   {
     "form_data": {
       "risk_level": "medium",
       "family_dynamics": "stable_with_concerns",
       "safety_concerns": ["inadequate_supervision", "substance_use"]
     }
   }
   ↓
6. FormSubmission status updates automatically:
   - draft → complete when all required fields filled
   - Section completion percentage updates
   - Template progress recalculates
```

### **9. Case Closure Flow**

```
User Story: Case manager closes completed case

1. All workflow templates completed (consent → registration → services → assessment)
2. Navigate to final template or case closure section
3. Fill closure form:
   - Closure reason
   - Outcomes achieved
   - Recommendations
   - Follow-up plan
   ↓
4. Submit closure:
   POST /api/form_submissions/component_submissions (closure template)
   ↓
5. Case status updates:
   - WorkflowProgress shows 100% completion
   - Case.status → 'closed'
   - All FormSubmissions locked (status: 'locked')
   ↓
6. Notifications sent:
   - Case team notified
   - Reports generated
   - Archive process initiated
```

## Current Implementation Status

### ✅ **Fully Implemented Systems**

#### Form Submission Lifecycle

**Auto-Creation**: Form submissions are automatically created for ALL active form templates when a case is created.

**Status Progression**: `Draft → In Progress → Submitted → Completed`

**Key Features**:

- **Uniqueness**: One submission per case+form_template combination
- **Auto-Status Updates**: Status changes automatically based on form data completion
- **Integration**: Seamless integration with case workflow and approval system

**Endpoints**:

```
POST   /api/form_submissions              # Create new submission
PATCH  /api/form_submissions/:id          # Update submission data
POST   /api/form_submissions/:id/submit   # Submit for completion
POST   /api/form_submissions/:id/calculate_fields # Calculate field values
```

#### Field Calculation System

**Comprehensive Calculation Types**: `age_from_date`, `sum`, `difference`, `percentage`, `average`, `multiply`, `divide`, `max`, `min`, `concat`, custom formulas

**Real-time Calculation Endpoints**:

```
POST /api/form_fields/:id/calculate                    # Individual field calculation
POST /api/form_templates/:id/calculate_all_fields      # All calculated fields
POST /api/form_templates/:id/recalculate_dependent_fields # Dependent field recalculation
```

**Service**: `FormFieldCalculationService` with production-ready calculation engine

#### Form Validation System

**Comprehensive Validation**: Field-level validations, required field checks, completion percentage tracking

**Validation Endpoints**:

```
POST /api/form_templates/:id/validate_data            # Validate form data against template
```

**Features**: Detailed error reporting, completion tracking, field-specific validation rules

### ⚠️ **Partially Implemented Systems**

#### Form Fields API

- **✅ Implemented**: `POST /api/form_fields/:id/calculate` - Individual field calculation (via FieldCalculationsController)
- **❌ Missing**: FormFieldsController - No dedicated controller for field management
- **✅ Implemented**: FormFieldSerializer with comprehensive attributes
- **❌ Missing**: `GET /api/form_fields` - Index action for field listing
- **❌ Missing**: `GET /api/form_sections/:id/form_fields` - Nested context-aware loading
- **❌ Missing**: `GET /api/form_fields/:id/lookup_options` - Per-field lookup data

#### Form Sections API

- **✅ Implemented**: FormSectionsController with JSONAPI format
- **✅ Implemented**: FormSectionSerializer with comprehensive attributes
- **❌ Missing**: Navigation meta data for progressive form flow

#### FormField Model

- **✅ Implemented**: `field_structure` method with context support
- **✅ Implemented**: `formatted_lookup_options` method
- **✅ Implemented**: Calculation and dependency logic
- **❌ Missing**: Context-aware `field_enabled?` and `field_visible?` methods

### 🚨 **Critical Issues**

#### Prerequisites System

- **❌ Problem**: Hardcoded `form_sequence` method in FormTemplate
- **❌ Problem**: Manual `setup_prerequisites!` method
- **❌ Problem**: Prerequisites duplicated in seed files
- **❌ Impact**: Cannot change prerequisites without code deployment

## Phase 1: Core API Implementation + Dynamic Sub-Form Architecture

### 1.0 Dynamic Sub-Form Architecture Implementation ⚠️ **NEW CRITICAL**

**Problem**: Figma shows modal dialogs for services and follow-ups that need to be dynamic and reusable
**Impact**: Current system cannot handle the "خطط التدخل وتفاصيل الخدمات" dialog and similar modal forms
**Solution**: Implement hierarchical FormSubmissions with sub-form templates + UX-focused section types

#### 1.0.0 FormSection Types (UX-Focused Architecture)

**New Approach**: Instead of technical boolean flags, use semantic section types that describe the user experience:

```ruby
enum :section_type, {
  form: 'form',    # Fields rendered directly in the page (traditional form sections)
  list: 'list'     # Collection of items managed via modal dialogs (services, follow-ups, etc.)
}
```

**Benefits**:

- ✅ **UX-focused** - describes how the section behaves for users
- ✅ **Intuitive naming** - developers immediately understand the difference
- ✅ **Extensible** - can add more types later (e.g., 'table', 'cards', 'timeline')
- ✅ **Clear validation** - form sections must have fields, list sections must have sub-form template

**Frontend Logic**:

```javascript
const FormSectionRenderer = ({ section, parentSubmission }) => {
  switch (section.section_type) {
    case "form":
      return <InlineFormSection section={section} />; // Fields in page
    case "list":
      return (
        <ItemListSection
          section={section}
          parentSubmission={parentSubmission}
        />
      ); // Modal management
    default:
      return <InlineFormSection section={section} />; // Fallback
  }
};
```

#### 1.0.1 Database Schema Changes for Sub-Forms

**Add Section Relationship to FormSubmissions**:

```ruby
class AddSectionToFormSubmissions < ActiveRecord::Migration[8.0]
  def change
    add_reference :form_submissions, :form_section, null: false, foreign_key: true
    add_column :form_submissions, :submission_order, :integer, null: false, default: 1

    add_index :form_submissions, [:form_section_id, :case_id]
    add_index :form_submissions, [:form_section_id, :submission_order]
  end
end
```

**Add Template Types to FormTemplates**:

```ruby
class AddTemplateTypeToFormTemplates < ActiveRecord::Migration[8.0]
  def change
    add_column :form_templates, :template_type, :string, default: 'workflow'
    add_column :form_templates, :sequence_order, :integer

    add_index :form_templates, :template_type
    add_index :form_templates, [:template_type, :sequence_order]
  end
end
```

**Add Section Type Support to FormSections**:

```ruby
class AddSectionTypeToFormSections < ActiveRecord::Migration[8.0]
  def change
    add_column :form_sections, :section_type, :string, default: 'form'
    add_column :form_sections, :sub_form_template_name, :string
    add_column :form_sections, :sub_form_button_label, :string
    add_column :form_sections, :max_sub_forms, :integer

    add_index :form_sections, :section_type
    add_index :form_sections, :sub_form_template_name
  end
end
```

#### 1.0.2 Model Updates for Hierarchical FormSubmissions

**Enhanced FormSubmission Model**:

```ruby
class FormSubmission < ApplicationRecord
  belongs_to :case
  belongs_to :form_section  # Every submission belongs to a section (NOT optional)

  # Get template through section
  delegate :form_template, to: :form_section

  validates :submission_order, presence: true

  # Scopes for different contexts
  scope :for_template, ->(template) { joins(:form_section).where(form_sections: { form_template: template }) }
  scope :for_section, ->(section) { where(form_section: section) }
  scope :workflow_submissions, -> { joins(form_section: :form_template).where(form_templates: { template_type: 'workflow' }) }
  scope :component_submissions, -> { joins(form_section: :form_template).where(form_templates: { template_type: 'component' }) }
  scope :ordered, -> { order(:submission_order) }

  # Helper methods
  def template_name
    form_section.form_template.name
  end

  def section_name
    form_section.name
  end

  def is_workflow_submission?
    form_section.form_template.workflow?
  end

  def is_component_submission?
    form_section.form_template.component?
  end
end
```

**Enhanced FormTemplate Model**:

```ruby
class FormTemplate < ApplicationRecord
  enum :template_type, {
    workflow: 'workflow',     # Main case workflow templates (consent, assessment, closure)
    component: 'component'    # Modal dialogs, standalone forms, surveys, etc.
  }

  has_many :form_sections, dependent: :destroy
  has_many :form_submissions, through: :form_sections

  validates :sequence_order, presence: true, if: :workflow?

  scope :workflow_templates, -> { where(template_type: 'workflow').order(:sequence_order) }
  scope :component_templates, -> { where(template_type: 'component') }

  # Template completion for a case
  def completed_for_case?(case_obj)
    return false if form_sections.empty?

    form_sections.all? { |section| section.completed_for_case?(case_obj) }
  end

  def progress_for_case(case_obj)
    return 0 if form_sections.empty?

    completed_sections = form_sections.count { |section| section.completed_for_case?(case_obj) }
    (completed_sections.to_f / form_sections.count * 100).round
  end
end
```

**Enhanced FormSection Model**:

```ruby
class FormSection < ApplicationRecord
  enum :section_type, {
    form: 'form',    # Traditional form fields rendered in page
    list: 'list'     # Collection of items managed via modal dialogs
  }

  # Validation rules based on section type
  validates :form_fields, presence: true, if: :form?
  validates :form_fields, absence: true, if: :list?
  validates :sub_form_template_name, presence: true, if: :list?
  validates :sub_form_template_name, absence: true, if: :form?

  def sub_form_template
    FormTemplate.find_by(name: sub_form_template_name) if list?
  end

  def submissions_for_case(case_obj)
    form_submissions.where(case: case_obj).ordered
  end

  def completed_for_case?(case_obj)
    if form? # Regular form section
      submission = form_submissions.find_by(case: case_obj)
      return false unless submission

      required_fields = form_fields.required
      required_fields.all? { |field| submission.get_field_value(field.field_name).present? }
    elsif list? # List section
      # Consider complete if has at least one submission (or based on business rules)
      submissions_for_case(case_obj).exists?
    end
  end

  # Helper methods for clarity
  def renders_fields_inline?
    form?
  end

  def manages_item_collection?
    list?
  end
end
```

#### 1.0.3 Template Creation with New Architecture

**Create Workflow Templates**:

```ruby
# Main combined form template (Step 3: المتابعة والخدمات)
follow_up_services_template = FormTemplate.create!(
  name: 'follow_up_and_services',
  title: 'المتابعة والخدمات',
  description: 'Combined follow-up and services management form',
  template_type: 'workflow',
  sequence_order: 3
)

# Other workflow templates
consent_template = FormTemplate.create!(
  name: 'consent_assent',
  title: 'الموافقة والإقرار',
  template_type: 'workflow',
  sequence_order: 1
)

assessment_template = FormTemplate.create!(
  name: 'comprehensive_assessment',
  title: 'التقييم الشامل',
  template_type: 'workflow',
  sequence_order: 4
)

# List-type sections in main form (manage collections via modals)
services_section = FormSection.create!(
  form_template: follow_up_services_template,
  name: 'service_instances',
  title: 'الخدمات',
  section_type: 'list',
  sub_form_template_name: 'service_instance',
  sub_form_button_label: 'إضافة خدمة +',
  max_sub_forms: 20
)

followups_section = FormSection.create!(
  form_template: follow_up_services_template,
  name: 'followup_instances',
  title: 'المتابعة',
  section_type: 'list',
  sub_form_template_name: 'followup_instance',
  sub_form_button_label: 'إضافة متابعة +',
  max_sub_forms: 50
)

```

**Create Component Templates** (for modal dialogs):

```ruby
# Service dialog template (component)
service_instance_template = FormTemplate.create!(
  name: 'service_instance',
  title: 'خطط التدخل وتفاصيل الخدمات',
  description: 'Individual service instance dialog',
  template_type: 'component'
)

# Follow-up dialog template (component)
followup_instance_template = FormTemplate.create!(
  name: 'followup_instance',
  title: 'تفاصيل المتابعة',
  description: 'Individual follow-up instance dialog',
  template_type: 'component'
)

# Form-type section in service dialog (renders fields inline)
service_details_section = FormSection.create!(
  form_template: service_instance_template,
  name: 'service_details',
  title: 'Service Details',
  section_type: 'form'  # Default, but explicit for clarity
)

# Create fields matching Figma dialog
FormField.create!(
  form_section: service_details_section,
  field_name: 'service_name',
  label: 'اسم التدخل / الخدمة التي سيتم تقديمها',
  field_type: 'text',
  required: true,
  display_order: 1
)

FormField.create!(
  form_section: service_details_section,
  field_name: 'intervention_reason',
  label: 'الحاجة الي التدخل / الخدمة هو معالجة',
  field_type: 'text',
  required: true,
  display_order: 2
)

FormField.create!(
  form_section: service_details_section,
  field_name: 'provider',
  label: 'مقدم التدخل',
  field_type: 'text',
  required: true,
  display_order: 3
)

FormField.create!(
  form_section: service_details_section,
  field_name: 'due_date',
  label: 'تاريخ الاستحقاق',
  field_type: 'date',
  required: true,
  display_order: 4
)

FormField.create!(
  form_section: service_details_section,
  field_name: 'monitoring_schedule',
  label: 'جدول المتابعة / المراقبة',
  field_type: 'select_field',
  required: true,
  display_order: 5
)
```

### 1.1 Fix Hardcoded Prerequisites System ⚠️ **CRITICAL**

**Problem**: Prerequisites are hardcoded in FormTemplate model and duplicated in seed files
**Impact**: Cannot change prerequisites without code deployment, not project-specific, technical debt

**Current Bad Implementation**:

```ruby
# BAD: Hardcoded in model
def self.form_sequence
  {
    'consent_assent' => [],
    'registration_rapid_assessment' => [ 'consent_assent' ],
    'comprehensive_assessment' => [ 'consent_assent', 'registration_rapid_assessment' ],
    'service_planning' => [ 'consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment' ]
  }
end

# BAD: Manual setup method
def self.setup_prerequisites!
  # Hardcoded logic
end

# BAD: Duplicated in every seed file
template.prerequisite_forms = ['consent_assent']  # Hardcoded!
```

**Solution**: Create FormTemplatePrerequisite mapping table

**New Migration**:

```ruby
class CreateFormTemplatePrerequisites < ActiveRecord::Migration[8.0]
  def change
    create_table :form_template_prerequisites do |t|
      t.references :form_template, null: false, foreign_key: true
      t.references :required_template, null: false, foreign_key: { to_table: :form_templates }
      t.boolean :active, default: true
      t.timestamps
    end

    add_index :form_template_prerequisites, [:form_template_id, :required_template_id], unique: true
  end
end
```

**New Models**:

```ruby
# app/models/form_template_prerequisite.rb
class FormTemplatePrerequisite < ApplicationRecord
  belongs_to :form_template
  belongs_to :required_template, class_name: 'FormTemplate'

  validates :form_template_id, uniqueness: {
    scope: :required_template_id,
    message: 'Prerequisite already exists for this form template'
  }

  scope :active, -> { where(active: true) }
end

# Updated app/models/form_template.rb
class FormTemplate < ApplicationRecord
  has_many :form_template_prerequisites, dependent: :destroy
  has_many :required_templates, through: :form_template_prerequisites

  # REMOVE hardcoded methods:
  # - self.form_sequence
  # - self.setup_prerequisites!
  # - prerequisite_forms JSON column

  def prerequisites_met?(case_obj)
    prerequisites = form_template_prerequisites.active.includes(:required_template)

    prerequisites.all? do |prereq|
      submission = case_obj.form_submissions.find_by(form_template: prereq.required_template)
      submission&.form_completed?
    end
  end
end
```

**Updated Serializer**:

```ruby
# Add to FormTemplateSerializer
has_many :required_templates, serializer: FormTemplateSerializer
```

### 1.2 Remove render_data Endpoint

**Action**: Immediately remove the monolithic `render_data` endpoint
**Location**: `app/controllers/api/form_templates_controller.rb`
**Reason**: Replace with granular, progressive loading approach

**Changes**:

```ruby
# REMOVE from FormTemplatesController
def render_data
  # ... entire method to be removed
end

# REMOVE from routes.rb
member do
  get :render_data      # Remove this line
end
```

### 1.2 Sub-Form API Endpoints ⚠️ **NEW CRITICAL**

**Purpose**: Add generic sub-form management endpoints to existing FormSubmissionsController
**Implementation**: Extend existing controller with hierarchical submission support

**New Routes**:

```ruby
# Add to existing form_submissions routes
resources :form_submissions do
  collection do
    post :component_submissions, action: :create_component_submission
  end

  member do
    get :section_submissions  # Get submissions for a specific section
  end
end
```

**Extended FormSubmissionsController**:

```ruby
class Api::FormSubmissionsController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!
  authorize_resources

  # GET /api/form_submissions/:id/section_submissions?section_id=123
  def section_submissions
    section = FormSection.find(params[:section_id])
    case_obj = @form_submission.case

    @submissions = section.submissions_for_case(case_obj)
    serialize_response(@submissions)
  end

  # POST /api/form_submissions/component_submissions
  def create_component_submission
    case_obj = Case.find(params[:case_id])
    section = FormSection.find(params[:form_section_id])

    # Validate section is list type and has component template
    unless section.list? && section.sub_form_template&.component?
      return serialize_errors({ detail: "Invalid section for component submission" }, :bad_request)
    end

    # Find the component template's main section
    component_template = section.sub_form_template
    component_section = component_template.form_sections.first

    @submission = FormSubmission.new(
      case: case_obj,
      form_section: component_section,
      form_data: params[:form_data] || {},
      created_by_id: current_user.id,
      submission_order: section.submissions_for_case(case_obj).count + 1
    )

    if @submission.save
      serialize_response(@submission, status: :created)
    else
      serialize_errors(@submission.errors)
    end
  end

  # POST /api/form_submissions (Enhanced for case creation)
  def create
    # Check if this is the first form for a new case
    if params[:case_id].blank? && first_form_template?
      # Create case and form submission together
      ActiveRecord::transaction do
        @case = Case.create!(case_creation_params)
        @form_submission = @case.form_submissions.create!(form_submission_params)

        # Create initial form submissions for other templates
        @case.create_initial_form_submissions
      end
    else
      # Normal flow - case already exists
      case_obj = Case.find(params[:case_id])
      @form_submission = case_obj.form_submissions.create!(form_submission_params)
    end

    serialize_response(@form_submission, status: :created)
  rescue ActiveRecord::RecordInvalid => e
    serialize_errors(e.record.errors)
  end

  # PATCH /api/form_submissions/:id (works for both root and sub submissions)
  def update
    if @form_submission.update(form_data: params[:form_data])
      serialize_response(@form_submission)
    else
      serialize_errors(@form_submission.errors)
    end
  end

  # DELETE /api/form_submissions/:id (works for both root and sub submissions)
  def destroy
    @form_submission.destroy!
    head :no_content
  end

  private

  def first_form_template?
    template = FormTemplate.find(params[:form_template_id])
    template.workflow? && template.sequence_order == 1
  end

  def case_creation_params
    {
      assigned_user_id: params[:assigned_user_id],
      case_type: params[:case_type] || 'general',
      priority_level: params[:priority_level] || 2,
      confidentiality_level: params[:confidentiality_level] || 0,
      created_by_id: current_user.id,
      project_id: current_project&.id,
      started_at: Time.current,
      # Beneficiary data from form
      beneficiary_name: params.dig(:form_data, :beneficiary_name),
      beneficiary_gender: params.dig(:form_data, :beneficiary_gender),
      beneficiary_age: params.dig(:form_data, :beneficiary_age),
      beneficiary_nationality: params.dig(:form_data, :beneficiary_nationality),
      beneficiary_phone: params.dig(:form_data, :beneficiary_phone),
      beneficiary_id_number: params.dig(:form_data, :beneficiary_id_number),
      beneficiary_date_of_birth: params.dig(:form_data, :beneficiary_date_of_birth),
      beneficiary_city: params.dig(:form_data, :beneficiary_city)
    }
  end

  def form_submission_params
    {
      form_template_id: params[:form_template_id],
      form_section_id: params[:form_section_id],
      form_data: params[:form_data] || {},
      created_by_id: current_user.id,
      status: :draft
    }
  end

  def next_submission_order(template_name)
    (@form_submission.sub_submissions_for_template(template_name)
                    .maximum(:submission_order) || 0) + 1
  end
end
```

### 1.3 Create New Form Fields Controller

**Current Status**: No FormFieldsController exists - only FieldCalculationsController handles calculations
**Need**: Create new FormFieldsController for field management + progressive loading + sub-form section detection

**Current Routes**:

```ruby
# ✅ Existing in routes.rb - handled by FieldCalculationsController
resources :form_fields, only: [] do
  member do
    post :calculate       # ✅ Routed to FieldCalculationsController#calculate
  end
end
```

**Routes to Add**:

```ruby
# Create new form_fields routes for field management
resources :form_fields, only: [:index] do  # Add index action
  member do
    post :calculate       # ✅ Keep existing (delegates to FieldCalculationsController)
    get :lookup_options   # Add lookup endpoint
  end
end

# Add nested route under form_sections
resources :form_sections, only: [:index, :show] do
  resources :form_fields, only: [:index]  # Nested context-aware loading
end
```

**Create New Controller**:

```ruby
# CREATE NEW: Api::FormFieldsController for field management
class Api::FormFieldsController < ApplicationController

  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!
  authorize_resources

  # ENHANCED: GET /api/form_sections/:form_section_id/form_fields?case_id=11
  def index
    if params[:form_section_id]
      form_section = FormSection.find(params[:form_section_id])

      # NEW: Handle list-type sections (item collections managed via modals)
      if form_section.list?
        # Return item collection management data instead of fields
        context = build_context(params)
        parent_submission = context[:form_submission]

        case_obj = context[:case]
        sub_submissions = case_obj ?
          form_section.submissions_for_case(case_obj) :
          FormSubmission.none

        response_data = {
          section_type: 'list',
          sub_form_template_name: form_section.sub_form_template_name,
          sub_form_button_label: form_section.sub_form_button_label,
          max_sub_forms: form_section.max_sub_forms,
          current_count: sub_submissions.count,
          can_add_more: !form_section.max_sub_forms || sub_submissions.count < form_section.max_sub_forms,
          sub_submissions: sub_submissions.map do |submission|
            {
              id: submission.id,
              form_data: submission.form_data,
              submission_order: submission.submission_order,
              created_at: submission.created_at,
              updated_at: submission.updated_at
            }
          end
        }

        return render json: { data: response_data }
      else
        # Form-type section with fields (rendered inline)
        @form_fields = form_section.form_fields.ordered

        # Apply context-aware conditional logic using existing field_structure method
        if params[:case_id] || params[:form_submission_id]
          context = build_context(params)
          @form_fields = @form_fields.map do |field|
            field_data = field.field_structure(context)
            # Add computed conditional states
            field_data[:is_enabled] = field_enabled?(field, context)
            field_data[:is_visible] = field_visible?(field, context)
            field_data
          end
        end
      end
    else
      @form_fields = FormField.all
    end

    serialize_response(@form_fields)
  end

  # ADD: GET /api/form_fields/:id/lookup_options
  def lookup_options
    # Use existing formatted_lookup_options method
    context = {
      current_project: current_project,
      locale: params[:locale] || 'en',
      search: params[:search],
      parent_id: params[:parent_id],
      filters: params[:filters] || {}
    }

    options = @form_field.formatted_lookup_options(current_project&.id, context)

    render json: {
      data: {
        field_id: @form_field.id,
        lookup_source_type: @form_field.lookup_source_type,
        options: options,
        meta: {
          total_options: options.count,
          search_term: params[:search],
          filters_applied: params[:filters]&.keys || []
        }
      }
    }
  end

  # POST /api/form_fields/:id/calculate - Delegate to FieldCalculationsController
  def calculate
    # Delegate to existing FieldCalculationsController
    redirect_to action: :calculate, controller: 'field_calculations', id: params[:id]
  end

  private

  def build_context(params)
    context = {}
    context[:case] = Case.find(params[:case_id]) if params[:case_id]
    context[:form_submission] = FormSubmission.find(params[:form_submission_id]) if params[:form_submission_id]
    context[:project_id] = current_project&.id
    context
  end

  def field_enabled?(field, context)
    # Build on existing conditional logic - implement based on field dependencies
    return true # Default implementation - to be enhanced
  end

  def field_visible?(field, context)
    # Build on existing visible attribute and conditional logic
    return field.visible? # Default implementation - to be enhanced
  end
end
```

### 1.4 Enhance FormFieldSerializer for Context-Aware States

**Current Status**: FormFieldSerializer exists with comprehensive attributes
**Need**: Add context-aware computed attributes for conditional logic

**Current Implementation**:

```ruby
# ✅ Already implemented in FormFieldSerializer
attribute :is_required do |object|
  object.required?
end

attribute :is_visible do |object|
  object.visible?  # Static - needs to be context-aware
end

attribute :is_calculated do |object|
  object.calculated_field?
end
```

**Enhancement Needed**:

```ruby
# Enhance FormFieldSerializer with context-aware attributes
class FormFieldSerializer
  include JSONAPI::Serializer

  # ✅ Keep existing comprehensive attributes
  attributes :field_name, :label, :field_type, :data_type, :display_order,
             :required, :visible, :help_text, :placeholder, :validation_rules,
             :field_config, :calculation_formula, :lookup_source_type,
             :lookup_source_config, :parent_field_id, :depends_on,
             :auto_calculate, :created_at, :updated_at

  # ENHANCE: Make these context-aware
  attribute :is_enabled do |object, params|
    context = params[:context] || {}
    object.field_enabled?(context)
  end

  attribute :is_visible do |object, params|
    context = params[:context] || {}
    object.field_visible?(context)
  end

  attribute :current_value do |object, params|
    context = params[:context] || {}
    form_submission = context[:form_submission]
    form_submission&.get_field_value(object.field_name)
  end

  # ✅ Keep existing computed attributes
  attribute :is_required, :is_calculated, :is_lookup, :has_options, :options_count

  # ✅ Keep existing relationships
  belongs_to :form_section
  has_many :form_field_options
  has_many :form_field_validations
end
```

**Add Methods to FormField Model**:

```ruby
# Add to app/models/form_field.rb
class FormField < ApplicationRecord
  # Build on existing field_structure method

  def field_enabled?(context = {})
    # Default implementation - enhance based on dependencies
    return true unless depends_on.present?

    form_submission = context[:form_submission]
    return true unless form_submission

    # Check if dependencies are met
    calculation_dependencies.all? do |dep_field|
      form_submission.get_field_value(dep_field).present?
    end
  end

  def field_visible?(context = {})
    # Build on existing visible attribute
    return false unless visible?

    # Add conditional logic based on form data
    form_submission = context[:form_submission]
    return true unless form_submission

    # Example: Hide field based on other field values
    # This can be enhanced with more complex conditional logic
    true
  end
end
```

### 1.5 Standardize Form Templates Response

**Current**: Raw JSON in `render_data` action
**Proposed**: JSONAPI format with FormTemplateSerializer

**Current FormTemplateSerializer**:

```ruby
class FormTemplateSerializer
  include JSONAPI::Serializer

  attributes :name, :title, :description, :active, :target_role,
             :sequence_order, :prerequisite_forms, :project_id,
             :form_type, :created_at, :updated_at

  # Computed attributes
  attribute :sections_count do |object|
    object.form_sections.count
  end

  attribute :fields_count do |object|
    object.form_fields.count
  end

  attribute :estimated_completion_time do |object|
    object.calculate_estimated_completion_time
  end

  # Relationships
  has_many :form_sections
  has_many :form_fields, through: :form_sections
end
```

### 1.4 Enhance Form Section Response with Navigation Meta

**Purpose**: Add navigation and progress data to form section responses
**Implementation**: Include meta data in FormSectionsController

**Enhanced Response**:

```ruby
# In FormSectionsController#show
def show
  navigation_meta = build_navigation_meta(@form_section, params)
  serialize_response(@form_section, meta: navigation_meta)
end

private

def build_navigation_meta(section, params)
  if params[:case_id] || params[:form_submission_id]
    context = build_context(params)
    {
      navigation: {
        current_section_id: section.id,
        next_section_id: section.next_accessible_section(context)&.id,
        previous_section_id: section.previous_section&.id,
        can_proceed: section.can_proceed?(context),
        completion_percentage: section.completion_percentage(context)
      },
      accessibility: {
        accessible_sections: section.form_template.accessible_section_ids(context),
        completed_sections: section.form_template.completed_section_ids(context)
      }
    }
  else
    {}
  end
end
```

## Phase 2: Field Conditional Logic

### 2.1 Enhanced FormField Serializer with Conditional States

**Purpose**: Add computed attributes for field visibility and enabled state
**Implementation**: Server-side calculation based on current form data

```json
{
  "data": {
    "id": "4",
    "type": "form_template_structure",
    "attributes": {
      "template_info": {
        "id": 4,
        "name": "service_planning",
        "title": "Service Planning",
        "form_type": "service_planning"
      },
      "completion_summary": {
        "overall_percentage": 65,
        "sections_count": 3,
        "completed_sections": 2,
        "accessible_sections": 2,
        "next_section": "case_closure"
      },
      "sections": [
        {
          "id": 5,
          "name": "goals_objectives",
          "title": "Goals and Objectives",
          "display_order": 1,
          "completion_status": {
            "is_complete": true,
            "percentage": 100,
            "completed_fields": 4,
            "total_fields": 4
          },
          "accessibility": {
            "is_accessible": true,
            "is_visible": true,
            "access_reason": "section_unlocked"
          },
          "fields": [
            {
              "id": 26,
              "field_name": "primary_goal",
              "label": "Primary Goal",
              "field_type": "textarea",
              "required": true,
              "completion_status": {
                "is_complete": true,
                "has_value": true,
                "is_valid": true
              },
              "field_config": {
                "max_length": 500,
                "placeholder": "Describe the primary goal..."
              },
              "current_value": "Improve family stability and safety",
              "lookup_options": null,
              "conditional_logic": null
            }
          ]
        }
      ]
    }
  }
}
```

### 2.2 Section Structure Enhancement

**Enhance**: `GET /api/form_sections/:id/structure`
**Add**: `section_structure` method to FormSection model

**Implementation**:

```ruby
# In FormSection model
def section_structure(form_submission = nil, context = {})
  {
    section_info: {
      id: id,
      name: name,
      title: title,
      description: description,
      display_order: display_order,
      is_required: is_required,
      visible: visible
    },
    completion_status: completion_status_for(form_submission),
    accessibility: accessibility_for(form_submission, context),
    fields: form_fields.visible.ordered.map do |field|
      field.field_structure(form_submission, context)
    end,
    metadata: {
      fields_count: form_fields.count,
      required_fields_count: form_fields.required.count,
      has_conditional_logic: form_fields.any? { |f| f.conditional_logic.present? }
    }
  }
end
```

### 2.3 Field Structure Enhancement

**Add**: `field_structure` method to FormField model (enhance existing)

**Enhanced Implementation**:

```ruby
# In FormField model
def field_structure(form_submission = nil, context = {})
  {
    field_info: {
      id: id,
      field_name: field_name,
      label: label,
      field_type: field_type,
      data_type: data_type,
      display_order: display_order,
      required: required,
      visible: visible,
      help_text: help_text,
      placeholder: placeholder
    },
    completion_status: completion_status_for(form_submission),
    field_config: parsed_field_config,
    current_value: form_submission&.get_field_value(id),
    lookup_options: formatted_lookup_options(context),
    validation_rules: validation_rules_structure,
    conditional_logic: parsed_conditional_logic,
    calculation_config: calculation_config_structure,
    dependencies: {
      depends_on: depends_on_fields,
      dependent_fields: dependent_fields_list
    }
  }
end
```

## Phase 3: Progressive Form Completion

### 3.1 Form Submission Completion API

**New Route**: `GET /api/form_submissions/:id/completion`
**Purpose**: Detailed completion status for entire form

**Response**:

```json
{
  "data": {
    "form_submission_id": 123,
    "overall_completion": {
      "percentage": 65,
      "status": "in_progress",
      "completed_sections": 2,
      "total_sections": 3,
      "next_required_section": "case_closure"
    },
    "sections_completion": [
      {
        "section_id": 5,
        "section_name": "goals_objectives",
        "completion_percentage": 100,
        "is_complete": true,
        "is_accessible": true,
        "completed_fields": 4,
        "total_fields": 4,
        "next_required_field": null
      }
    ],
    "validation_summary": {
      "has_errors": false,
      "error_count": 0,
      "errors_by_section": {}
    }
  }
}
```

### 3.2 Section Accessibility Logic

**Add**: Methods to determine section accessibility

**Implementation**:

```ruby
# In FormSection model
def accessibility_for(form_submission, context = {})
  {
    is_accessible: is_accessible_for?(form_submission, context),
    is_visible: is_visible_for?(context),
    access_reason: determine_access_reason(form_submission, context),
    prerequisites_met: prerequisites_met?(form_submission),
    user_role_allowed: user_role_allowed?(context[:user_role]),
    project_type_allowed: project_type_allowed?(context[:project_type])
  }
end

def is_accessible_for?(form_submission, context = {})
  return true if display_order == 1 # First section always accessible
  return false unless is_visible_for?(context)
  return false unless prerequisites_met?(form_submission)

  previous_sections_complete?(form_submission)
end
```

## Phase 4: Lookup System Integration

### 4.1 Field-Specific Lookup Endpoints

**New Route**: `GET /api/form_fields/:id/lookup_options`
**Purpose**: Get lookup options for specific field with filtering

**Implementation**:

```ruby
# In FormFieldsController
def lookup_options
  options = @form_field.formatted_lookup_options(
    current_project: current_project,
    locale: params[:locale] || 'en',
    search: params[:search],
    parent_id: params[:parent_id],
    filters: params[:filters] || {}
  )

  render json: {
    data: {
      field_id: @form_field.id,
      lookup_source_type: @form_field.lookup_source_type,
      options: options,
      meta: {
        total_options: options.count,
        search_term: params[:search],
        filters_applied: params[:filters]&.keys || []
      }
    }
  }
end
```

### 4.2 Bulk Lookup Data API

**New Route**: `GET /api/form_templates/:id/lookup_data`
**Purpose**: Get all lookup data for form template in single request

**Response**:

```json
{
  "data": {
    "form_template_id": 4,
    "lookup_data": {
      "staff_positions": [
        {"id": 1, "name": "Case Manager", "department": "Protection"}
      ],
      "geographic_locations": [
        {"id": 1, "name": "Amman", "location_type": "city", "parent_id": 5}
      ],
      "case_types": [
        {"id": 1, "name": "Child Protection", "category": "protection"}
      ]
    },
    "hierarchical_data": {
      "geographic_locations": {
        "countries": [...],
        "governorates_by_country": {...},
        "cities_by_governorate": {...}
      }
    }
  }
}
```

## Phase 5: Performance Optimization

### 5.1 Caching Strategy

**Implement**: Multi-level caching for form data

**Cache Keys**:

- `form_template_structure:#{template_id}:#{user_role}:#{project_id}`
- `lookup_data:#{source_type}:#{project_id}:#{filters_hash}`
- `field_options:#{field_id}:#{context_hash}`

### 5.2 Database Query Optimization

**Optimize**: Eager loading for complex form structures

**Implementation**:

```ruby
# In FormTemplate model
scope :with_complete_structure, -> {
  includes(
    form_sections: {
      form_fields: [
        :form_field_options,
        :form_field_validations,
        :field_type_definition
      ]
    }
  )
}
```

## Implementation Timeline

### Week 1: Core API Implementation + Sub-Form Architecture + Case Creation Integration

- [ ] ⚠️ **NEW: Implement case creation within form flow** - Modify FormSubmissionsController to create case + form submission together
- [ ] ⚠️ **NEW: Add case creation parameters** - Extract beneficiary data from first form to populate case columns
- [ ] ⚠️ **NEW: Implement dynamic sub-form architecture** - Database migrations for hierarchical FormSubmissions
- [ ] ⚠️ **NEW: Create sub-form templates** - Service instance and follow-up instance templates
- [ ] ⚠️ **NEW: Add sub-form API endpoints** - Extend FormSubmissionsController with sub-form management
- [ ] ⚠️ **Fix hardcoded prerequisites system** - Create FormTemplatePrerequisite mapping table
- [ ] **Remove hardcoded methods** from FormTemplate (form_sequence, setup_prerequisites!)
- [ ] **Remove `render_data` endpoint** from FormTemplatesController
- [ ] **Create new FormFieldsController** - Add `index` and `lookup_options` actions + sub-form detection
- [ ] **Add nested routes** for form_fields under form_sections
- [ ] **Add context-aware methods** to FormField model (field_enabled?, field_visible?)

### Week 2: Field Conditional Logic + Sub-Form Frontend + Case Creation Testing

- [ ] **NEW: Test case creation flow** - Verify first form creates case + form submission correctly
- [ ] **NEW: Test beneficiary data sync** - Ensure form data populates case beneficiary columns
- [ ] **NEW: Frontend case creation components** - New case form flow without separate case creation step
- [ ] **NEW: Frontend sub-form components** - SubFormContainer, SubFormModal, SubSubmissionCard
- [ ] **NEW: Test sub-form workflow** - Service and follow-up dialog creation/editing
- [ ] **Enhance FormFieldSerializer** with context-aware computed attributes (`is_enabled`, `is_visible`)
- [ ] **Implement context-aware field states** in FormField model
- [ ] **Add context building** for form submissions and cases in new FormFieldsController
- [ ] **Test field state calculations** with real form data
- [x] ✅ **Form submission lifecycle** (already implemented)
- [x] ✅ **Field calculation system** (already implemented via FieldCalculationsController)
- [x] ✅ **Form validation system** (already implemented)
- [x] ✅ **FormFieldSerializer base implementation** (already comprehensive)

### Week 3: Navigation and Progress

- [ ] **Add navigation meta data** to FormSectionsController
- [ ] **Implement section accessibility logic**
- [ ] **Add progress tracking methods** to models
- [ ] **Test navigation flow**
- [ ] **Integrate existing FieldCalculationsController** with new FormFieldsController

### Week 4: Lookup Integration & Sub-Form Testing

- [ ] **NEW: Comprehensive sub-form testing** - All modal dialog workflows
- [ ] **NEW: Sub-form performance testing** - Large numbers of service instances
- [ ] **Implement per-field lookup endpoints**
- [ ] **Optimize lookup data loading**
- [ ] **Comprehensive testing** of all new endpoints
- [ ] **Performance testing** and optimization
- [ ] **Test integration** with existing FieldCalculationsController and validation systems

### Week 5: Documentation & Cleanup + Sub-Form Migration

- [ ] **NEW: Migrate existing service data** - Convert current service data to sub-form submissions
- [ ] **NEW: Update Figma workflow** - Configure step 3 as combined follow_up_and_services form
- [ ] **Update API documentation** (include sub-form endpoints + existing FieldCalculationsController endpoints)
- [ ] **Remove deprecated code**
- [ ] **Frontend integration testing**
- [ ] **Performance monitoring** setup

## Success Metrics

### Technical Metrics

- [ ] All endpoints return consistent JSONAPI format
- [ ] API response times improved by 30%
- [ ] Reduced number of API calls for form loading by 50%
- [ ] 100% test coverage for new endpoints

### Developer Experience

- [ ] Consistent API patterns across all form endpoints
- [ ] Comprehensive API documentation
- [ ] Clear error handling and validation responses
- [ ] Easy integration with frontend components

### User Experience

- [ ] Faster form loading times
- [ ] Progressive form completion guidance
- [ ] Dynamic field behavior based on user context
- [ ] Better error feedback and validation

## Risk Mitigation

### Backward Compatibility

- Maintain existing endpoints during transition
- Use feature flags for new API responses
- Gradual migration of frontend components

### Performance Impact

- Implement caching before releasing new endpoints
- Monitor database query performance
- Use database indexes for new filtering patterns

### Testing Strategy

- Unit tests for all new controllers and methods
- Integration tests for API endpoints
- Performance tests for complex form structures
- Frontend integration testing

## Phase 6: Advanced Features

### 6.1 Form Builder Support APIs

**Purpose**: Support for dynamic form creation and management

**New Endpoints**:

```ruby
# Form Template Management
POST   /api/form_templates              # Create new template
PATCH  /api/form_templates/:id          # Update template
DELETE /api/form_templates/:id          # Delete template
POST   /api/form_templates/:id/clone    # Clone template

# Section Management
POST   /api/form_sections               # Create new section
PATCH  /api/form_sections/:id           # Update section
DELETE /api/form_sections/:id           # Delete section
POST   /api/form_sections/:id/reorder   # Reorder sections

# Field Management
POST   /api/form_fields                 # Create new field
PATCH  /api/form_fields/:id             # Update field
DELETE /api/form_fields/:id             # Delete field
POST   /api/form_fields/:id/reorder     # Reorder fields
```

### 6.2 Form Validation Engine

**New Route**: `POST /api/form_templates/:id/validate`
**Purpose**: Comprehensive form validation with detailed error reporting

**Request**:

```json
{
  "form_data": {
    "26": "Primary goal text",
    "27": "2025-06-01"
  },
  "validation_level": "strict", // strict, normal, draft
  "section_id": 5 // optional: validate specific section
}
```

**Response**:

```json
{
  "data": {
    "is_valid": false,
    "validation_level": "strict",
    "overall_completion": 75,
    "errors": [
      {
        "field_id": 28,
        "field_name": "target_date",
        "error_type": "required",
        "message": "Target date is required",
        "section_id": 5
      }
    ],
    "warnings": [
      {
        "field_id": 26,
        "field_name": "primary_goal",
        "warning_type": "length",
        "message": "Consider providing more detail"
      }
    ],
    "field_validations": {
      "26": { "is_valid": true, "warnings": ["length"] },
      "27": { "is_valid": true },
      "28": { "is_valid": false, "errors": ["required"] }
    }
  }
}
```

### 6.3 Form Analytics and Insights

**New Routes**:

```ruby
GET /api/form_templates/:id/analytics    # Form usage analytics
GET /api/form_templates/:id/completion_stats # Completion statistics
GET /api/form_fields/:id/usage_stats     # Field-specific usage data
```

## Phase 7: Integration Enhancements

### 7.1 Real-time Form Collaboration

**WebSocket Integration**: Real-time form editing and collaboration

**Features**:

- Live form editing indicators
- Real-time field value updates
- Collaborative comments on fields
- Form lock/unlock mechanisms

### 7.2 Form Workflow Integration

**New Routes**:

```ruby
POST /api/form_submissions/:id/workflow_actions # Trigger workflow actions
GET  /api/form_submissions/:id/workflow_status  # Get workflow status
POST /api/form_submissions/:id/assign           # Assign to user
POST /api/form_submissions/:id/escalate         # Escalate for review
```

### 7.3 Document Generation Integration

**New Routes**:

```ruby
POST /api/form_submissions/:id/generate_document # Generate PDF/Word document
GET  /api/form_templates/:id/document_templates  # Available document templates
POST /api/form_templates/:id/preview_document    # Preview generated document
```

## Phase 8: Mobile and Offline Support

### 8.1 Mobile-Optimized Endpoints

**Features**:

- Compressed response formats
- Progressive data loading
- Image optimization for mobile
- Reduced payload sizes

### 8.2 Offline Synchronization

**New Routes**:

```ruby
GET  /api/form_templates/:id/offline_package # Complete offline package
POST /api/form_submissions/sync              # Sync offline submissions
GET  /api/sync/status                        # Sync status and conflicts
```

## Phase 9: Security and Compliance

### 9.1 Enhanced Security Features

**Implementations**:

- Field-level encryption for sensitive data
- Audit trails for all form modifications
- Role-based field access control
- Data retention policies

### 9.2 Compliance Features

**New Routes**:

```ruby
GET  /api/form_submissions/:id/audit_trail   # Complete audit trail
POST /api/form_submissions/:id/anonymize     # Anonymize sensitive data
GET  /api/compliance/data_export             # GDPR data export
POST /api/compliance/data_deletion           # Right to be forgotten
```

## Technical Implementation Details

### Database Schema Enhancements

**New Tables**:

```sql
-- Form template versions for change tracking
CREATE TABLE form_template_versions (
  id BIGSERIAL PRIMARY KEY,
  form_template_id BIGINT REFERENCES form_templates(id),
  version_number INTEGER NOT NULL,
  changes_summary JSONB,
  created_by BIGINT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Form collaboration sessions
CREATE TABLE form_collaboration_sessions (
  id BIGSERIAL PRIMARY KEY,
  form_submission_id BIGINT REFERENCES form_submissions(id),
  user_id BIGINT,
  session_token VARCHAR(255),
  last_activity TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Field-level audit trails
CREATE TABLE form_field_audit_logs (
  id BIGSERIAL PRIMARY KEY,
  form_submission_id BIGINT REFERENCES form_submissions(id),
  form_field_id BIGINT REFERENCES form_fields(id),
  old_value JSONB,
  new_value JSONB,
  changed_by BIGINT,
  changed_at TIMESTAMP DEFAULT NOW(),
  change_reason VARCHAR(255)
);
```

### API Rate Limiting and Throttling

**Implementation**:

```ruby
# In ApplicationController
include Rack::Attack

# Rate limiting for form APIs
throttle('form_api/ip', limit: 300, period: 5.minutes) do |req|
  req.ip if req.path.start_with?('/api/form')
end

# Stricter limits for form creation
throttle('form_creation/user', limit: 10, period: 1.hour) do |req|
  req.env['current_user']&.id if req.post? && req.path.match?(/\/api\/form_(templates|sections|fields)$/)
end
```

### Error Handling Standardization

**Global Error Handler**:

```ruby
# In ApplicationController
rescue_from StandardError do |exception|
  case exception
  when ActiveRecord::RecordNotFound
    serialize_errors({
      code: 'RESOURCE_NOT_FOUND',
      detail: 'The requested resource was not found',
      source: { parameter: params[:id] }
    }, :not_found)
  when ActiveRecord::RecordInvalid
    serialize_errors({
      code: 'VALIDATION_FAILED',
      detail: 'Validation failed',
      source: { errors: exception.record.errors.full_messages }
    }, :unprocessable_entity)
  when AtharAuth::AuthorizationError
    serialize_errors({
      code: 'AUTHORIZATION_FAILED',
      detail: 'Insufficient permissions',
      source: { required_permission: exception.required_permission }
    }, :forbidden)
  end
end
```

## Testing Strategy

### Unit Testing Requirements

```ruby
# Controller Tests
describe Api::FormFieldsController do
  describe 'GET #index' do
    it 'returns paginated form fields'
    it 'filters by form_section_id'
    it 'filters by field_type'
    it 'applies sorting correctly'
    it 'handles authorization properly'
  end

  describe 'GET #lookup_options' do
    it 'returns field-specific lookup options'
    it 'applies search filtering'
    it 'handles hierarchical data'
    it 'respects project scoping'
  end

  describe 'POST #calculate' do
    it 'delegates to FieldCalculationsController'
    it 'maintains existing calculation functionality'
  end
end

# Model Tests
describe FormSection do
  describe '#section_structure' do
    it 'returns complete section structure'
    it 'includes completion status'
    it 'handles conditional field visibility'
    it 'calculates accessibility correctly'
  end
end
```

### Integration Testing

```ruby
# API Integration Tests
describe 'Form Structure API' do
  it 'loads complete form structure efficiently'
  it 'handles large forms without timeout'
  it 'maintains data consistency across endpoints'
  it 'supports concurrent form editing'
end

# Performance Tests
describe 'Form API Performance' do
  it 'loads form structure under 500ms'
  it 'handles 100 concurrent requests'
  it 'maintains response time with large datasets'
  it 'efficiently caches lookup data'
end
```

## Monitoring and Observability

### Metrics to Track

```ruby
# Custom Metrics
- form_api_response_time_seconds
- form_structure_cache_hit_ratio
- form_validation_error_rate
- form_completion_rate_by_template
- lookup_data_load_time_seconds

# Business Metrics
- forms_completed_per_day
- average_form_completion_time
- most_used_form_templates
- field_abandonment_rates
- user_form_interaction_patterns
```

### Logging Strategy

```ruby
# Structured Logging
Rails.logger.info({
  event: 'form_structure_loaded',
  form_template_id: template.id,
  user_id: current_user.id,
  response_time_ms: response_time,
  cache_hit: cache_hit?,
  sections_count: sections.count,
  fields_count: total_fields
}.to_json)
```

## Deployment Strategy

### Feature Flags

```ruby
# Use feature flags for gradual rollout
if FeatureFlag.enabled?(:new_form_structure_api, current_user)
  # Use new API format
else
  # Use legacy API format
end
```

### Blue-Green Deployment

1. Deploy new API endpoints alongside existing ones
2. Gradually migrate frontend components
3. Monitor performance and error rates
4. Complete migration when stable
5. Remove legacy endpoints after migration

### Rollback Plan

1. Immediate: Feature flag toggle to disable new APIs
2. Short-term: Database rollback for schema changes
3. Long-term: Code rollback if major issues discovered

## Conclusion

This comprehensive plan addresses the current inconsistencies in the AtharCM Form System APIs while adding the missing functionality needed to support the Figma designs. The phased approach ensures minimal disruption to existing functionality while providing significant improvements to developer experience and system performance.

The plan prioritizes critical fixes and standardization first, followed by enhanced functionality and performance optimizations. Each phase builds upon the previous one, ensuring a stable and consistent API evolution.

## Key Decisions Summary

### ✅ **Agreed Approach**

1. ⚠️ **Fix hardcoded prerequisites system** - Replace with FormTemplatePrerequisite mapping table
2. **Remove `render_data` immediately** - Replace monolithic approach with granular APIs
3. **Create FormFieldsController** - New controller for field management (separate from existing FieldCalculationsController)
4. **Nested resources**: `/form_sections/:id/form_fields?case_id=11`
5. **Per-field lookup loading** - Load lookup data only when needed
6. **Server-side conditional logic** - Calculate field states on server
7. **Simple refresh strategy** - Re-fetch section fields after saves
8. **JSONAPI standard** - Consistent response format across all endpoints

### 🎯 **Core Benefits**

- **Performance**: Load only what's needed (progressive loading)
- **Scalability**: Works with large forms without timeout issues
- **Consistency**: JSONAPI standards across all form-related endpoints
- **Flexibility**: Frontend controls loading granularity
- **Real-time**: Field conditions calculated with current form data
- **Simplicity**: No complex caching or real-time infrastructure needed

### 📋 **API Structure**

```
GET /api/cases                                          # Load cases
GET /api/form_templates                                 # Left navigation
GET /api/form_sections/:id                             # Section info + navigation meta
GET /api/form_sections/:id/form_fields?case_id=11      # Context-aware fields OR list-type item collection data
GET /api/form_fields/:id/lookup_options                # Per-field lookup data

EXISTING: Field Calculation APIs (FieldCalculationsController)
POST /api/form_fields/:id/calculate                    # Individual field calculation
POST /api/field_calculations/calculate_all_fields      # Calculate all fields for template
POST /api/field_calculations/recalculate_dependent_fields # Recalculate dependent fields

NEW: Component Submission APIs
GET /api/form_submissions/:id/section_submissions?section_id=123  # List submissions for a section
POST /api/form_submissions/component_submissions        # Create new component submission (modal dialog)
PATCH /api/form_submissions/:id                         # Update submission (same for all types)
DELETE /api/form_submissions/:id                        # Delete submission (same for all types)

NEW: Case Progress APIs
GET /api/cases/:id/current_template                     # Get current template and workflow progress
GET /api/cases/:id/template_progress/:template_id       # Get section progress for specific template
GET /api/cases/:id/progress                             # Get comprehensive progress with caching
```

### 🔧 **Technical Implementation**

- **Field States**: `is_enabled`, `is_visible` computed attributes
- **Context Awareness**: Based on `case_id` or `form_submission_id`
- **Navigation Meta**: Included in form section responses
- **Conditional Logic**: Server-side evaluation for accuracy
- **Performance**: On-demand calculation (no caching complexity)
- **Form Submissions**: Auto-created lifecycle with status progression
- **Real-time Calculations**: Existing FieldCalculationsController with comprehensive calculation engine
- **Validation**: Existing field-level and form-level validation integration
- **NEW: FormFieldsController**: Dedicated controller for field management (separate from calculations)
- **NEW: Section-Based Submissions**: Every FormSubmission belongs to a FormSection (no hierarchies)
- **NEW: Template Types**: `workflow` (main case flow) vs `component` (modal dialogs)
- **NEW: List Sections**: FormSections with `section_type: 'list'` that manage item collections via modals
- **NEW: Dynamic Modal System**: Reusable for services, follow-ups, family members, incidents, etc.
- **NEW: Concerns-Based Status Tracking**: Clean, maintainable status management with performance optimization
- **NEW: Real-time Progress Updates**: WebSocket-based progress notifications with background calculation

### 🔗 **Integration with Existing Systems**

**Form Submission Discovery in Progressive Loading**:

```ruby
# In NEW FormFieldsController#index
case_obj = Case.find(params[:case_id])
form_submission = case_obj.form_submissions
                          .find_by(form_template: form_section.form_template)
# Use existing form_submission for context
```

**Real-time Calculations During Field Editing** (using existing FieldCalculationsController):

```javascript
// User changes field → Save → Trigger recalculation → Refresh
await saveFieldValue(fieldId, newValue);
await fetch(`/api/field_calculations/recalculate_dependent_fields`, {
  method: "POST",
  body: JSON.stringify({
    form_template_id: templateId,
    changed_field_name: fieldName,
    form_data: formData,
  }),
});
await fetchSectionFields(sectionId, { case_id: caseId });
```

**Form Validation Before Section Completion**:

```javascript
// Use existing validation endpoint
const validation = await fetch(
  `/api/form_templates/${templateId}/validate_data`
);
if (!validation.is_valid) showValidationErrors(validation.validation_errors);
```

This plan transforms the AtharCM Form System from a monolithic approach to a modern, granular API that supports progressive loading and provides excellent developer and user experience.

## NEW: Dynamic Sub-Form Architecture Summary

### **Revolutionary Modal Dialog System**

The plan now includes a groundbreaking dynamic sub-form architecture that transforms modal dialogs into actual form templates with hierarchical FormSubmissions:

**Key Innovation**: The Figma dialog "خطط التدخل وتفاصيل الخدمات" becomes a `service_instance` FormTemplate, and each time a user fills it out, it creates a child FormSubmission under the parent `follow_up_and_services` submission.

### **Architecture Benefits**

1. **Completely Dynamic**: Works for services, follow-ups, family members, incidents, etc.
2. **Reuses Existing Infrastructure**: No new models, extends FormSubmission + FormTemplate
3. **Matches Figma Exactly**: Service dialog becomes service_instance form template
4. **Configuration-Driven**: Add new sub-form types via admin interface
5. **Maintains All Features**: Validation, calculations, conditional logic, permissions
6. **Simple Frontend**: Same FormRenderer component for all dialogs
7. **Queryable Data**: Can report across all service instances, follow-ups, etc.

### **Data Structure**

```
Case #123
├── FormSubmission (section: consent_info) ← Consent section
├── FormSubmission (section: assent_info) ← Assent section
├── FormSubmission (section: registration_details) ← Registration section
├── FormSubmission (section: services_list) ← Services management section
├── FormSubmission (section: service_details) ← Service instance #1
├── FormSubmission (section: service_details) ← Service instance #2
├── FormSubmission (section: followups_list) ← Follow-ups management section
├── FormSubmission (section: followup_details) ← Follow-up instance #1
├── FormSubmission (section: followup_details) ← Follow-up instance #2
├── FormSubmission (section: assessment_basic) ← Assessment section
└── FormSubmission (section: closure_details) ← Closure section
```

**Template Structure**:

```
workflow templates:
├── consent_assent (sequence: 1)
│   ├── consent_info (form section)
│   └── assent_info (form section)
├── registration_rapid_assessment (sequence: 2)
│   └── registration_details (form section)
├── follow_up_and_services (sequence: 3)
│   ├── services_list (list section) → uses service_instance template
│   └── followups_list (list section) → uses followup_instance template
└── comprehensive_assessment (sequence: 4)
    └── assessment_basic (form section)

component templates:
├── service_instance
│   └── service_details (form section)
└── followup_instance
    └── followup_details (form section)
```

## **Status Tracking Architecture**

### **Overview: Concerns-Based Status Management**

The status tracking system uses Rails concerns to separate responsibilities and maintain clean, testable code:

1. **FormSubmission::StatusTracking** - Individual submission status
2. **FormSection::CompletionRules** - Section-level completion logic
3. **FormTemplate::ProgressCalculation** - Template-level progress
4. **Case::WorkflowProgress** - Case-level workflow tracking with caching

### **1. FormSubmission Status (Stored)**

```ruby
# app/models/concerns/form_submission/status_tracking.rb
module FormSubmission::StatusTracking
  extend ActiveSupport::Concern

  included do
    enum :status, {
      draft: 'draft',           # User started, not complete
      complete: 'complete',     # All required fields filled
      submitted: 'submitted',   # Explicitly submitted for review
      locked: 'locked'          # Cannot be edited
    }

    validates :completed_at, presence: true, if: :complete?
    validates :submitted_at, presence: true, if: :submitted?

    after_update :update_completion_status
    after_update :invalidate_case_progress, if: :status_changed?
  end

  def mark_complete!
    update!(status: 'complete', completed_at: Time.current)
  end

  def all_required_fields_complete?
    form_section.required_fields.all? { |field| has_value_for?(field) }
  end

  private

  def update_completion_status
    if draft? && all_required_fields_complete?
      update_columns(status: 'complete', completed_at: Time.current)
    elsif complete? && !all_required_fields_complete?
      update_columns(status: 'draft', completed_at: nil)
    end
  end

  def invalidate_case_progress
    case.invalidate_workflow_progress!
  end
end

# Usage in model
class FormSubmission < ApplicationRecord
  include FormSubmission::StatusTracking
  # ... other logic
end
```

### **2. Section Completion Rules (Computed)**

```ruby
# app/models/concerns/form_section/completion_rules.rb
module FormSection::CompletionRules
  extend ActiveSupport::Concern

  def completed_for_case?(case_obj)
    case section_type
    when 'form'
      form_section_complete?(case_obj)
    when 'list'
      list_section_complete?(case_obj)
    end
  end

  def completion_percentage_for_case(case_obj)
    case section_type
    when 'form'
      form_section_percentage(case_obj)
    when 'list'
      list_section_percentage(case_obj)
    end
  end

  private

  def list_section_complete?(case_obj)
    submissions = submissions_for_case(case_obj).where(status: ['complete', 'submitted'])
    completion_rule_class.new(self, submissions).satisfied?
  end

  def completion_rule_class
    "#{name.classify}CompletionRule".safe_constantize || DefaultListCompletionRule
  end
end

# Section-specific completion rules
class ServicesListCompletionRule < BaseCompletionRule
  def satisfied?
    submissions.count >= 1  # At least one service required
  end
end

class GoalsListCompletionRule < BaseCompletionRule
  def satisfied?
    submissions.count >= 2 && has_required_goal_types?
  end

  private

  def has_required_goal_types?
    goal_types = submissions.map { |s| s.form_data['goal_type'] }.compact
    ['short_term', 'long_term'].all? { |type| goal_types.include?(type) }
  end
end

class FollowupsListCompletionRule < BaseCompletionRule
  def satisfied?
    true  # Optional section
  end
end
```

### **3. Template Progress Calculation**

```ruby
# app/models/concerns/form_template/progress_calculation.rb
module FormTemplate::ProgressCalculation
  extend ActiveSupport::Concern

  def completed_for_case?(case_obj)
    required_sections.all? { |section| section.completed_for_case?(case_obj) }
  end

  def progress_for_case(case_obj)
    return 0 if form_sections.empty?

    completed_count = form_sections.count { |section| section.completed_for_case?(case_obj) }
    (completed_count.to_f / form_sections.count * 100).round
  end

  def detailed_progress_for_case(case_obj)
    {
      template_id: id,
      template_name: name,
      is_completed: completed_for_case?(case_obj),
      overall_progress: progress_for_case(case_obj),
      sections: form_sections.map { |section| section_progress(section, case_obj) }
    }
  end
end
```

### **4. Case Workflow Progress (Cached)**

```ruby
# app/models/concerns/case/workflow_progress.rb
module Case::WorkflowProgress
  extend ActiveSupport::Concern

  included do
    has_one :workflow_progress, dependent: :destroy
  end

  def current_template
    workflow_progress&.current_template || refresh_workflow_progress!.current_template
  end

  def refresh_workflow_progress!
    progress_data = WorkflowProgressCalculator.new(self).calculate

    if workflow_progress
      workflow_progress.update!(progress_data)
    else
      create_workflow_progress!(progress_data)
    end

    workflow_progress
  end

  def invalidate_workflow_progress!
    workflow_progress&.mark_stale!
  end
end

# Service class for complex calculations
class WorkflowProgressCalculator
  def initialize(case_obj)
    @case = case_obj
  end

  def calculate
    {
      current_template: find_current_template,
      completed_templates: completed_templates_count,
      total_templates: total_templates_count,
      overall_progress: calculate_overall_progress,
      last_updated_at: Time.current,
      stale: false
    }
  end

  private

  def find_current_template
    FormTemplate.workflow_templates.find { |template| !template.completed_for_case?(@case) }
  end
end

# Presenter for API responses
class WorkflowProgressPresenter
  def initialize(case_obj)
    @case = case_obj
  end

  def summary
    {
      current_template: current_template_data,
      workflow_progress: workflow_progress_data,
      sections_progress: sections_progress_data,
      meta: meta_data
    }
  end
end
```

### **5. Performance-Optimized Progress API**

```ruby
# app/controllers/api/cases_controller.rb
class Api::CasesController < ApplicationController
  # GET /api/cases/:id/progress
  def progress
    @case = Case.find(params[:id])

    # Use cached data if fresh, trigger background refresh if stale
    if @case.workflow_progress&.fresh?
      render json: WorkflowProgressPresenter.new(@case).summary
    else
      RefreshWorkflowProgressJob.perform_later(@case.id)
      render json: WorkflowProgressPresenter.new(@case).summary
    end
  end
end

# Background job for expensive calculations
class RefreshWorkflowProgressJob < ApplicationJob
  def perform(case_id)
    case_obj = Case.find(case_id)
    case_obj.refresh_workflow_progress!

    # Broadcast real-time update to frontend
    ActionCable.server.broadcast(
      "case_#{case_id}_progress",
      WorkflowProgressPresenter.new(case_obj).summary
    )
  end
end
```

### **6. Database Schema for Progress Tracking**

```ruby
# Migration: Add workflow progress caching
class CreateWorkflowProgresses < ActiveRecord::Migration[7.0]
  def change
    create_table :workflow_progresses do |t|
      t.references :case, null: false, foreign_key: true
      t.references :current_template, null: true, foreign_key: { to_table: :form_templates }
      t.integer :completed_templates, null: false, default: 0
      t.integer :total_templates, null: false, default: 0
      t.integer :overall_progress, null: false, default: 0
      t.boolean :stale, null: false, default: false
      t.datetime :last_updated_at, null: false

      t.timestamps
    end

    add_index :workflow_progresses, :case_id, unique: true
    add_index :workflow_progresses, :stale
  end
end

# Migration: Add status tracking to form submissions
class AddStatusToFormSubmissions < ActiveRecord::Migration[7.0]
  def change
    add_column :form_submissions, :status, :string, null: false, default: 'draft'
    add_column :form_submissions, :completed_at, :datetime
    add_column :form_submissions, :submitted_at, :datetime

    add_index :form_submissions, :status
    add_index :form_submissions, [:case_id, :status]
  end
end

# Migration: Add required flag to form sections
class AddRequiredToFormSections < ActiveRecord::Migration[7.0]
  def change
    add_column :form_sections, :required, :boolean, null: false, default: true

    add_index :form_sections, [:form_template_id, :required]
  end
end
```

### **Frontend Integration**

```javascript
// Generic SubFormModal component reuses existing FormRenderer!
const SubFormModal = ({ template, onSave, onCancel }) => {
  const [formData, setFormData] = useState({});

  return (
    <Modal title={template.title}>
      <FormRenderer
        formTemplate={template}
        formData={formData}
        onChange={setFormData}
      />
      <button onClick={() => onSave(formData)}>اضافة</button>
    </Modal>
  );
};
```

### **Progress Tracking Frontend Integration**

```javascript
// Real-time progress updates
const CaseProgressTracker = ({ caseId }) => {
  const [progress, setProgress] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);

  useEffect(() => {
    // Load initial progress
    loadProgress();

    // Subscribe to real-time updates
    const progressChannel = consumer.subscriptions.create(
      { channel: "CaseProgressChannel", case_id: caseId },
      {
        received(data) {
          setProgress(data);
          setIsCalculating(false);
          showNotification("Progress updated");
        },
      }
    );

    return () => progressChannel.unsubscribe();
  }, [caseId]);

  const loadProgress = async () => {
    const response = await fetch(`/api/cases/${caseId}/progress`);
    const data = await response.json();

    setProgress(data);
    setIsCalculating(data.workflow_progress?.is_calculating || false);
  };

  if (!progress) return <LoadingSpinner />;

  return (
    <div className="progress-tracker">
      <div className="overall-progress">
        <h3>Case Progress: {progress.workflow_progress.overall_progress}%</h3>
        <ProgressBar value={progress.workflow_progress.overall_progress} />
        {isCalculating && <span className="calculating">Updating...</span>}
      </div>

      <div className="current-template">
        <h4>Current: {progress.current_template?.title}</h4>
        <ProgressBar value={progress.current_template?.progress} />
      </div>

      <div className="sections-progress">
        {progress.sections_progress.map((section) => (
          <SectionProgressCard
            key={section.section_id}
            section={section}
            isCurrent={section.is_current}
          />
        ))}
      </div>
    </div>
  );
};

// Updated service dialog with new API
const ServiceDialog = ({ sectionId, caseId }) => {
  const [services, setServices] = useState([]);

  const loadServices = async () => {
    const response = await fetch(
      `/api/form_submissions/1/section_submissions?section_id=${sectionId}`
    );
    setServices(response.data);
  };

  const addService = async (serviceData) => {
    const response = await fetch(
      "/api/form_submissions/component_submissions",
      {
        method: "POST",
        body: JSON.stringify({
          case_id: caseId,
          form_section_id: sectionId,
          form_data: serviceData,
        }),
      }
    );

    if (response.ok) {
      loadServices(); // Refresh list
      // Progress will update automatically via WebSocket
    }
  };

  return (
    <div>
      <h3>الخدمات</h3>
      {services.map((service) => (
        <ServiceCard key={service.id} service={service} />
      ))}
      <button onClick={() => setShowDialog(true)}>إضافة خدمة +</button>

      {showDialog && (
        <ServiceInstanceDialog
          onSave={addService}
          onCancel={() => setShowDialog(false)}
        />
      )}
    </div>
  );
};
```

This dynamic sub-form system represents a major architectural advancement that provides maximum flexibility while maintaining consistency with existing patterns.

### **UX-Focused Section Types**

The plan introduces a semantic approach to FormSection types that focuses on user experience rather than technical implementation:

**`section_type: 'form'`** - Traditional form sections

- Fields rendered directly in the page
- Used for single sets of data (Goals, Assessment details, etc.)
- Example: Registration form, Assessment questions

**`section_type: 'list'`** - Item collection management sections

- Shows list of existing items + "Add +" button
- Items managed via modal dialogs
- Used for multiple instances of data (Services, Follow-ups, Family members, etc.)
- Example: Services section with "إضافة خدمة +" button

**Benefits of This Approach**:

- ✅ **Intuitive** - Any developer immediately understands the UX difference
- ✅ **Semantic** - Names describe user experience, not technical details
- ✅ **Extensible** - Can add 'table', 'cards', 'timeline' types in future
- ✅ **Clear Validation** - form sections must have fields, list sections must have sub-form template
- ✅ **Frontend Simplicity** - Switch statement handles all rendering logic

This approach transforms complex technical concepts into simple, UX-focused categories that make the system much easier to understand and extend.
